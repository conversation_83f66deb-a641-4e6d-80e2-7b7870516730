package config

import (
	"encoding/json"
	"os"
)

type Config struct {
	ServerPort     string `json:"server_port"`
	DatabasePath   string `json:"database_path"`
	OpenVPNPath    string `json:"openvpn_path"`
	ConfigDir      string `json:"config_dir"`
	CertDir        string `json:"cert_dir"`
	LogLevel       string `json:"log_level"`
	JWTSecret      string `json:"jwt_secret"`
	AdminUsername  string `json:"admin_username"`
	AdminPassword  string `json:"admin_password"`
}

func Load() (*Config, error) {
	cfg := &Config{
		ServerPort:     "8080",
		DatabasePath:   "./data/vpn-manager.db",
		OpenVPNPath:    "/usr/sbin/openvpn",
		ConfigDir:      "./data/openvpn",
		CertDir:        "./data/certs",
		LogLevel:       "info",
		JWTSecret:      "your-secret-key-change-this",
		AdminUsername:  "admin",
		AdminPassword:  "admin123",
	}

	// Try to load from config file
	if data, err := os.ReadFile("config.json"); err == nil {
		if err := json.Unmarshal(data, cfg); err != nil {
			return nil, err
		}
	}

	// Override with environment variables if present
	if port := os.Getenv("SERVER_PORT"); port != "" {
		cfg.ServerPort = port
	}
	if dbPath := os.Getenv("DATABASE_PATH"); dbPath != "" {
		cfg.DatabasePath = dbPath
	}
	if ovpnPath := os.Getenv("OPENVPN_PATH"); ovpnPath != "" {
		cfg.OpenVPNPath = ovpnPath
	}
	if configDir := os.Getenv("CONFIG_DIR"); configDir != "" {
		cfg.ConfigDir = configDir
	}
	if certDir := os.Getenv("CERT_DIR"); certDir != "" {
		cfg.CertDir = certDir
	}
	if logLevel := os.Getenv("LOG_LEVEL"); logLevel != "" {
		cfg.LogLevel = logLevel
	}
	if jwtSecret := os.Getenv("JWT_SECRET"); jwtSecret != "" {
		cfg.JWTSecret = jwtSecret
	}
	if adminUser := os.Getenv("ADMIN_USERNAME"); adminUser != "" {
		cfg.AdminUsername = adminUser
	}
	if adminPass := os.Getenv("ADMIN_PASSWORD"); adminPass != "" {
		cfg.AdminPassword = adminPass
	}

	return cfg, nil
}
