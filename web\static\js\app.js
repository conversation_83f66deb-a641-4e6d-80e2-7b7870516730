// VPN Manager Frontend JavaScript

class VPNManager {
    constructor() {
        this.apiBase = '/api/v1';
        this.token = localStorage.getItem('vpn_token');
        this.init();
    }

    init() {
        // Initialize event listeners
        this.setupEventListeners();
        
        // Start periodic updates if on dashboard
        if (window.location.pathname === '/') {
            this.startPeriodicUpdates();
        }
    }

    setupEventListeners() {
        // Server control buttons
        const startBtn = document.getElementById('start-server');
        const stopBtn = document.getElementById('stop-server');
        const restartBtn = document.getElementById('restart-server');
        const refreshLogsBtn = document.getElementById('refresh-logs');

        if (startBtn) startBtn.addEventListener('click', () => this.controlServer('start'));
        if (stopBtn) stopBtn.addEventListener('click', () => this.controlServer('stop'));
        if (restartBtn) restartBtn.addEventListener('click', () => this.controlServer('restart'));
        if (refreshLogsBtn) refreshLogsBtn.addEventListener('click', () => this.loadLogs());
    }

    async apiRequest(endpoint, options = {}) {
        const url = `${this.apiBase}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...(this.token && { 'Authorization': `Bearer ${this.token}` })
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (response.status === 401) {
                // Token expired or invalid
                localStorage.removeItem('vpn_token');
                window.location.href = '/login';
                return;
            }

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API request failed:', error);
            this.showAlert('API request failed: ' + error.message, 'danger');
            throw error;
        }
    }

    async controlServer(action) {
        try {
            const button = document.getElementById(`${action}-server`);
            if (button) {
                button.disabled = true;
                button.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${action.charAt(0).toUpperCase() + action.slice(1)}ing...`;
            }

            await this.apiRequest(`/server/${action}`, { method: 'POST' });
            this.showAlert(`Server ${action} command sent successfully`, 'success');
            
            // Refresh status after a short delay
            setTimeout(() => this.updateServerStatus(), 2000);
        } catch (error) {
            this.showAlert(`Failed to ${action} server: ${error.message}`, 'danger');
        } finally {
            const button = document.getElementById(`${action}-server`);
            if (button) {
                button.disabled = false;
                button.innerHTML = `<i class="fas fa-${this.getActionIcon(action)}"></i> ${action.charAt(0).toUpperCase() + action.slice(1)}`;
            }
        }
    }

    getActionIcon(action) {
        const icons = {
            start: 'play',
            stop: 'stop',
            restart: 'redo'
        };
        return icons[action] || 'cog';
    }

    async updateServerStatus() {
        try {
            const status = await this.apiRequest('/server/status');
            
            // Update status display
            const statusElement = document.getElementById('server-status');
            const detailedStatusElement = document.getElementById('detailed-status');
            
            if (statusElement) {
                statusElement.textContent = status.is_running ? 'Running' : 'Stopped';
                statusElement.className = status.is_running ? 'text-success' : 'text-danger';
            }
            
            if (detailedStatusElement) {
                detailedStatusElement.textContent = status.is_running ? 'Online' : 'Offline';
                detailedStatusElement.className = `badge ${status.is_running ? 'bg-success' : 'bg-danger'}`;
            }

            // Update uptime
            const uptimeElement = document.getElementById('uptime');
            if (uptimeElement && status.start_time) {
                const uptime = this.calculateUptime(status.start_time);
                uptimeElement.textContent = uptime;
            }

        } catch (error) {
            console.error('Failed to update server status:', error);
        }
    }

    async updateDashboardStats() {
        try {
            const [status, clients, connections] = await Promise.all([
                this.apiRequest('/status'),
                this.apiRequest('/clients'),
                this.apiRequest('/connections')
            ]);

            // Update connected clients count
            const connectedElement = document.getElementById('connected-clients');
            if (connectedElement) {
                connectedElement.textContent = status.connected_clients || 0;
            }

            // Update total clients count
            const totalElement = document.getElementById('total-clients');
            if (totalElement) {
                totalElement.textContent = clients.length || 0;
            }

            // Update data transfer
            const dataElement = document.getElementById('data-transfer');
            if (dataElement && status.bytes_received && status.bytes_sent) {
                const totalMB = Math.round((status.bytes_received + status.bytes_sent) / (1024 * 1024));
                dataElement.textContent = `${totalMB} MB`;
            }

            // Update active connections table
            this.updateActiveConnections(connections);

        } catch (error) {
            console.error('Failed to update dashboard stats:', error);
        }
    }

    updateActiveConnections(connections) {
        const tbody = document.getElementById('active-connections');
        if (!tbody) return;

        if (!connections || connections.length === 0) {
            tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">No active connections</td></tr>';
            return;
        }

        tbody.innerHTML = connections.map(conn => `
            <tr>
                <td>${conn.common_name}</td>
                <td>${conn.virtual_address}</td>
                <td>${this.formatTime(conn.connected_since)}</td>
            </tr>
        `).join('');
    }

    async loadLogs() {
        try {
            const logs = await this.apiRequest('/logs');
            const container = document.getElementById('log-container');
            
            if (container && logs) {
                container.innerHTML = logs.map(log => 
                    `<div class="log-entry ${log.level}">[${this.formatTime(log.timestamp)}] ${log.message}</div>`
                ).join('');
                
                // Scroll to bottom
                container.scrollTop = container.scrollHeight;
            }
        } catch (error) {
            console.error('Failed to load logs:', error);
        }
    }

    startPeriodicUpdates() {
        // Update immediately
        this.updateServerStatus();
        this.updateDashboardStats();
        this.loadLogs();

        // Set up periodic updates
        setInterval(() => {
            this.updateServerStatus();
            this.updateDashboardStats();
        }, 5000); // Every 5 seconds

        setInterval(() => {
            this.loadLogs();
        }, 10000); // Every 10 seconds
    }

    calculateUptime(startTime) {
        const start = new Date(startTime);
        const now = new Date();
        const diff = now - start;

        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

        if (days > 0) {
            return `${days}d ${hours}h ${minutes}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    }

    formatTime(timestamp) {
        return new Date(timestamp).toLocaleString();
    }

    showAlert(message, type = 'info') {
        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert at top of main container
        const main = document.querySelector('main');
        if (main) {
            main.insertBefore(alert, main.firstChild);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new VPNManager();
});
