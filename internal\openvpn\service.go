package openvpn

import (
	"bufio"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
	"time"
	"vpn-manager/internal/config"
	"vpn-manager/internal/models"
)

type Service struct {
	config    *config.Config
	configDir string
	certDir   string
	pidFile   string
	logFile   string
}

func NewService(cfg *config.Config) *Service {
	return &Service{
		config:    cfg,
		configDir: cfg.ConfigDir,
		certDir:   cfg.CertDir,
		pidFile:   filepath.Join(cfg.ConfigDir, "openvpn.pid"),
		logFile:   filepath.Join(cfg.ConfigDir, "openvpn.log"),
	}
}

func (s *Service) Initialize() error {
	// Create necessary directories
	dirs := []string{s.configDir, s.certDir}
	for _, dir := range dirs {
		if err := os.Mkdir<PERSON>ll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %v", dir, err)
		}
	}

	// Initialize PKI if not exists
	if err := s.initializePKI(); err != nil {
		return fmt.Errorf("failed to initialize PKI: %v", err)
	}

	return nil
}

func (s *Service) GenerateServerConfig(serverConfig *models.ServerConfig) error {
	configPath := filepath.Join(s.configDir, "server.conf")

	config := fmt.Sprintf(`# OpenVPN Server Configuration
port %d
proto %s
dev tun

# Certificates and keys
ca %s
cert %s
key %s
dh %s

# Network configuration
server %s %s
ifconfig-pool-persist %s

# Security
cipher %s
auth %s
tls-auth %s 0
key-direction 0

# Compression
%s

# Client configuration
client-config-dir %s
client-to-client %s
duplicate-cn %s

# Networking
push "redirect-gateway def1 bypass-dhcp" %s
push "dhcp-option DNS %s"
push "dhcp-option DNS %s"

# Connection settings
keepalive %s
max-clients %d

# Logging
status %s
log-append %s
verb 3
mute 20

# Daemon
daemon
writepid %s
`,
		serverConfig.Port,
		serverConfig.Protocol,
		filepath.Join(s.certDir, "ca.crt"),
		filepath.Join(s.certDir, "server.crt"),
		filepath.Join(s.certDir, "server.key"),
		filepath.Join(s.certDir, "dh.pem"),
		serverConfig.Network,
		serverConfig.Netmask,
		filepath.Join(s.configDir, "ipp.txt"),
		serverConfig.Cipher,
		serverConfig.Auth,
		filepath.Join(s.certDir, "ta.key"),
		s.getCompressionConfig(serverConfig.Compression),
		filepath.Join(s.configDir, "ccd"),
		s.boolToString(serverConfig.ClientToClient),
		s.boolToString(serverConfig.DuplicateCN),
		s.boolToString(serverConfig.RedirectGateway),
		serverConfig.DNS1,
		serverConfig.DNS2,
		serverConfig.KeepAlive,
		serverConfig.MaxClients,
		filepath.Join(s.configDir, "status.log"),
		s.logFile,
		s.pidFile,
	)

	return os.WriteFile(configPath, []byte(config), 0644)
}

func (s *Service) Start() error {
	if s.IsRunning() {
		return fmt.Errorf("OpenVPN server is already running")
	}

	configPath := filepath.Join(s.configDir, "server.conf")
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return fmt.Errorf("server configuration file not found: %s", configPath)
	}

	cmd := exec.Command(s.config.OpenVPNPath, "--config", configPath)

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start OpenVPN: %v", err)
	}

	// Wait a moment to see if the process starts successfully
	time.Sleep(2 * time.Second)

	if !s.IsRunning() {
		return fmt.Errorf("OpenVPN failed to start - check logs for details")
	}

	return nil
}

func (s *Service) Stop() error {
	pid, err := s.getPID()
	if err != nil {
		return fmt.Errorf("failed to get OpenVPN PID: %v", err)
	}

	if pid == 0 {
		return fmt.Errorf("OpenVPN is not running")
	}

	process, err := os.FindProcess(pid)
	if err != nil {
		return fmt.Errorf("failed to find process: %v", err)
	}

	if err := process.Signal(syscall.SIGTERM); err != nil {
		return fmt.Errorf("failed to send SIGTERM: %v", err)
	}

	// Wait for graceful shutdown
	for i := 0; i < 10; i++ {
		if !s.IsRunning() {
			// Clean up PID file
			os.Remove(s.pidFile)
			return nil
		}
		time.Sleep(1 * time.Second)
	}

	// Force kill if graceful shutdown failed
	if err := process.Signal(syscall.SIGKILL); err != nil {
		return fmt.Errorf("failed to force kill process: %v", err)
	}

	os.Remove(s.pidFile)
	return nil
}

func (s *Service) Restart() error {
	if s.IsRunning() {
		if err := s.Stop(); err != nil {
			return fmt.Errorf("failed to stop OpenVPN: %v", err)
		}
	}

	return s.Start()
}

func (s *Service) IsRunning() bool {
	pid, err := s.getPID()
	if err != nil || pid == 0 {
		return false
	}

	process, err := os.FindProcess(pid)
	if err != nil {
		return false
	}

	// Send signal 0 to check if process exists
	err = process.Signal(syscall.Signal(0))
	return err == nil
}

func (s *Service) GetStatus() (*models.ServerStatus, error) {
	status := &models.ServerStatus{
		IsRunning: s.IsRunning(),
	}

	if status.IsRunning {
		pid, err := s.getPID()
		if err == nil {
			status.PID = pid
		}

		// Get start time from PID file modification time
		if info, err := os.Stat(s.pidFile); err == nil {
			status.StartTime = info.ModTime()
		}

		// Get connection statistics
		if stats, err := s.getConnectionStats(); err == nil {
			status.ConnectedClients = stats["connected_clients"].(int)
			status.TotalConnections = stats["total_connections"].(int)
			status.BytesReceived = stats["bytes_received"].(int64)
			status.BytesSent = stats["bytes_sent"].(int64)
		}
	}

	return status, nil
}

func (s *Service) getPID() (int, error) {
	if _, err := os.Stat(s.pidFile); os.IsNotExist(err) {
		return 0, nil
	}

	data, err := os.ReadFile(s.pidFile)
	if err != nil {
		return 0, err
	}

	pidStr := strings.TrimSpace(string(data))
	pid, err := strconv.Atoi(pidStr)
	if err != nil {
		return 0, err
	}

	return pid, nil
}

func (s *Service) getConnectionStats() (map[string]interface{}, error) {
	statusFile := filepath.Join(s.configDir, "status.log")

	stats := map[string]interface{}{
		"connected_clients": 0,
		"total_connections": 0,
		"bytes_received":    int64(0),
		"bytes_sent":        int64(0),
	}

	if _, err := os.Stat(statusFile); os.IsNotExist(err) {
		return stats, nil
	}

	file, err := os.Open(statusFile)
	if err != nil {
		return stats, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	connectedClients := 0
	var totalBytesReceived, totalBytesSent int64

	for scanner.Scan() {
		line := scanner.Text()
		if strings.HasPrefix(line, "CLIENT_LIST") {
			connectedClients++
			// Parse bytes from status line if available
			// Format: CLIENT_LIST,client_name,real_address,virtual_address,bytes_received,bytes_sent,connected_since
			parts := strings.Split(line, ",")
			if len(parts) >= 6 {
				if bytesReceived, err := strconv.ParseInt(parts[4], 10, 64); err == nil {
					totalBytesReceived += bytesReceived
				}
				if bytesSent, err := strconv.ParseInt(parts[5], 10, 64); err == nil {
					totalBytesSent += bytesSent
				}
			}
		}
	}

	stats["connected_clients"] = connectedClients
	stats["bytes_received"] = totalBytesReceived
	stats["bytes_sent"] = totalBytesSent

	return stats, nil
}

func (s *Service) boolToString(b bool) string {
	if b {
		return ""
	}
	return "#"
}

func (s *Service) getCompressionConfig(compression string) string {
	switch compression {
	case "lz4-v2":
		return "compress lz4-v2"
	case "lz4":
		return "compress lz4"
	case "lzo":
		return "compress lzo"
	default:
		return "#compress"
	}
}

func (s *Service) initializePKI() error {
	// Check if CA already exists
	caPath := filepath.Join(s.certDir, "ca.crt")
	if _, err := os.Stat(caPath); err == nil {
		return nil // PKI already initialized
	}

	// Create basic PKI structure - in production, use proper CA tools like easy-rsa
	// This is a simplified version for demonstration

	// Create client config directory
	ccdDir := filepath.Join(s.configDir, "ccd")
	if err := os.MkdirAll(ccdDir, 0755); err != nil {
		return err
	}

	// For now, we'll assume certificates are provided externally
	// In a production system, you would integrate with easy-rsa or similar

	return s.createPlaceholderCerts()
}

func (s *Service) createPlaceholderCerts() error {
	// Create placeholder certificate files
	// In production, these should be real certificates

	placeholderContent := "# Placeholder certificate file\n# Replace with actual certificates in production\n"

	certFiles := []string{
		"ca.crt",
		"server.crt",
		"server.key",
		"dh.pem",
		"ta.key",
	}

	for _, file := range certFiles {
		path := filepath.Join(s.certDir, file)
		if err := os.WriteFile(path, []byte(placeholderContent), 0644); err != nil {
			return err
		}
	}

	return nil
}
