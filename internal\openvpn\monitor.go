package openvpn

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
	"vpn-manager/internal/models"
)

type Monitor struct {
	service *Service
}

func NewMonitor(service *Service) *Monitor {
	return &Monitor{
		service: service,
	}
}

func (m *Monitor) GetServerLogs(lines int) ([]string, error) {
	logFile := m.service.logFile

	if _, err := os.Stat(logFile); os.IsNotExist(err) {
		return []string{}, nil
	}

	file, err := os.Open(logFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open log file: %v", err)
	}
	defer file.Close()

	var logLines []string
	scanner := bufio.NewScanner(file)

	// Read all lines first
	var allLines []string
	for scanner.Scan() {
		allLines = append(allLines, scanner.Text())
	}

	// Return last N lines
	start := len(allLines) - lines
	if start < 0 {
		start = 0
	}

	logLines = allLines[start:]
	return logLines, nil
}

func (m *Monitor) GetConnectionStatus() ([]*models.ActiveClient, error) {
	statusFile := filepath.Join(m.service.configDir, "status.log")

	if _, err := os.Stat(statusFile); os.IsNotExist(err) {
		return []*models.ActiveClient{}, nil
	}

	file, err := os.Open(statusFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open status file: %v", err)
	}
	defer file.Close()

	var clients []*models.ActiveClient
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := scanner.Text()

		// Parse CLIENT_LIST entries
		// Format: CLIENT_LIST,Common Name,Real Address,Virtual Address,Bytes Received,Bytes Sent,Connected Since
		if strings.HasPrefix(line, "CLIENT_LIST") {
			parts := strings.Split(line, ",")
			if len(parts) >= 7 {
				client := &models.ActiveClient{
					CommonName:     parts[1],
					RealAddress:    parts[2],
					VirtualAddress: parts[3],
				}

				// Parse bytes received
				if bytesReceived, err := strconv.ParseInt(parts[4], 10, 64); err == nil {
					client.BytesReceived = bytesReceived
				}

				// Parse bytes sent
				if bytesSent, err := strconv.ParseInt(parts[5], 10, 64); err == nil {
					client.BytesSent = bytesSent
				}

				// Parse connected since timestamp
				if connectedSince, err := time.Parse("Mon Jan 2 15:04:05 2006", parts[6]); err == nil {
					client.ConnectedSince = connectedSince
				}

				clients = append(clients, client)
			}
		}
	}

	return clients, nil
}

func (m *Monitor) GetServerStatistics() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Get basic server status
	serverStatus, err := m.service.GetStatus()
	if err != nil {
		return nil, err
	}

	stats["is_running"] = serverStatus.IsRunning
	stats["pid"] = serverStatus.PID
	stats["start_time"] = serverStatus.StartTime
	stats["connected_clients"] = serverStatus.ConnectedClients
	stats["total_connections"] = serverStatus.TotalConnections
	stats["bytes_received"] = serverStatus.BytesReceived
	stats["bytes_sent"] = serverStatus.BytesSent

	// Get additional statistics from status file
	statusFile := filepath.Join(m.service.configDir, "status.log")
	if _, err := os.Stat(statusFile); err == nil {
		if fileStats, err := m.parseStatusFile(statusFile); err == nil {
			for k, v := range fileStats {
				stats[k] = v
			}
		}
	}

	return stats, nil
}

func (m *Monitor) parseStatusFile(statusFile string) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	file, err := os.Open(statusFile)
	if err != nil {
		return stats, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	var maxBandwidth int64 = 0
	var totalSessions int = 0

	for scanner.Scan() {
		line := scanner.Text()

		// Parse routing table entries
		if strings.HasPrefix(line, "ROUTING_TABLE") {
			totalSessions++
		}

		// Parse global stats if available
		if strings.HasPrefix(line, "GLOBAL_STATS") {
			// Parse global statistics if OpenVPN provides them
			parts := strings.Split(line, ",")
			if len(parts) > 1 {
				// Process global stats
			}
		}

		// Calculate bandwidth usage
		if strings.HasPrefix(line, "CLIENT_LIST") {
			parts := strings.Split(line, ",")
			if len(parts) >= 6 {
				if bytesReceived, err := strconv.ParseInt(parts[4], 10, 64); err == nil {
					if bytesSent, err := strconv.ParseInt(parts[5], 10, 64); err == nil {
						totalBytes := bytesReceived + bytesSent
						if totalBytes > maxBandwidth {
							maxBandwidth = totalBytes
						}
					}
				}
			}
		}
	}

	stats["total_sessions"] = totalSessions
	stats["max_bandwidth_usage"] = maxBandwidth

	return stats, nil
}

func (m *Monitor) GetSystemResources() (map[string]interface{}, error) {
	resources := make(map[string]interface{})

	// Get process information if server is running
	if m.service.IsRunning() {
		pid, err := m.service.getPID()
		if err == nil && pid > 0 {
			// In a production system, you would get actual process statistics
			// For now, we'll provide placeholder values
			resources["cpu_usage"] = 0.0
			resources["memory_usage"] = 0
			resources["open_files"] = 0
			resources["network_connections"] = 0
		}
	}

	// Get disk usage for log and config directories
	if configSize, err := m.getDirSize(m.service.configDir); err == nil {
		resources["config_dir_size"] = configSize
	}

	if certSize, err := m.getDirSize(m.service.certDir); err == nil {
		resources["cert_dir_size"] = certSize
	}

	return resources, nil
}

func (m *Monitor) getDirSize(path string) (int64, error) {
	var size int64

	err := filepath.Walk(path, func(_ string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return nil
	})

	return size, err
}

func (m *Monitor) GetRecentEvents(hours int) ([]map[string]interface{}, error) {
	var events []map[string]interface{}

	// Parse log file for recent events
	logs, err := m.GetServerLogs(1000) // Get last 1000 lines
	if err != nil {
		return events, err
	}

	// cutoff := time.Now().Add(-time.Duration(hours) * time.Hour)
	// TODO: Use cutoff to filter events by time in production

	for _, logLine := range logs {
		// Parse log line for timestamp and event type
		// This is a simplified parser - in production you'd want more robust parsing

		if strings.Contains(logLine, "CONNECTED") ||
			strings.Contains(logLine, "DISCONNECTED") ||
			strings.Contains(logLine, "ERROR") ||
			strings.Contains(logLine, "WARNING") {

			event := map[string]interface{}{
				"timestamp": time.Now(), // In production, parse actual timestamp
				"message":   logLine,
				"level":     m.getLogLevel(logLine),
			}

			events = append(events, event)
		}
	}

	return events, nil
}

func (m *Monitor) getLogLevel(logLine string) string {
	if strings.Contains(logLine, "ERROR") {
		return "error"
	}
	if strings.Contains(logLine, "WARNING") {
		return "warning"
	}
	if strings.Contains(logLine, "CONNECTED") || strings.Contains(logLine, "DISCONNECTED") {
		return "info"
	}
	return "debug"
}

func (m *Monitor) IsHealthy() (bool, []string) {
	var issues []string

	// Check if service is running
	if !m.service.IsRunning() {
		issues = append(issues, "OpenVPN service is not running")
	}

	// Check if configuration files exist
	configFile := filepath.Join(m.service.configDir, "server.conf")
	if _, err := os.Stat(configFile); os.IsNotExist(err) {
		issues = append(issues, "Server configuration file is missing")
	}

	// Check if certificate files exist
	certFiles := []string{"ca.crt", "server.crt", "server.key", "dh.pem", "ta.key"}
	for _, file := range certFiles {
		path := filepath.Join(m.service.certDir, file)
		if _, err := os.Stat(path); os.IsNotExist(err) {
			issues = append(issues, fmt.Sprintf("Certificate file missing: %s", file))
		}
	}

	// Check log file size (warn if too large)
	if info, err := os.Stat(m.service.logFile); err == nil {
		if info.Size() > 100*1024*1024 { // 100MB
			issues = append(issues, "Log file is very large and may need rotation")
		}
	}

	return len(issues) == 0, issues
}
