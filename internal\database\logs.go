package database

import (
	"database/sql"
	"time"
	"vpn-manager/internal/models"
)

type LogRepository struct {
	db *sql.DB
}

func NewLogRepository(db *sql.DB) *LogRepository {
	return &LogRepository{db: db}
}

func (r *LogRepository) Create(log *models.ConnectionLog) error {
	query := `
		INSERT INTO connection_logs (client_name, client_ip, connected_at, bytes_received, bytes_sent)
		VALUES (?, ?, ?, ?, ?)
	`
	
	result, err := r.db.Exec(query, log.ClientName, log.ClientIP, log.ConnectedAt, log.BytesReceived, log.BytesSent)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	log.ID = int(id)
	return nil
}

func (r *LogRepository) GetByID(id int) (*models.ConnectionLog, error) {
	query := `
		SELECT id, client_name, client_ip, connected_at, disconnected_at, bytes_received, bytes_sent
		FROM connection_logs
		WHERE id = ?
	`
	
	log := &models.ConnectionLog{}
	err := r.db.QueryRow(query, id).Scan(
		&log.ID,
		&log.ClientName,
		&log.ClientIP,
		&log.ConnectedAt,
		&log.DisconnectedAt,
		&log.BytesReceived,
		&log.BytesSent,
	)
	
	if err != nil {
		return nil, err
	}
	
	return log, nil
}

func (r *LogRepository) List(limit int) ([]*models.ConnectionLog, error) {
	query := `
		SELECT id, client_name, client_ip, connected_at, disconnected_at, bytes_received, bytes_sent
		FROM connection_logs
		ORDER BY connected_at DESC
		LIMIT ?
	`
	
	rows, err := r.db.Query(query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var logs []*models.ConnectionLog
	for rows.Next() {
		log := &models.ConnectionLog{}
		err := rows.Scan(
			&log.ID,
			&log.ClientName,
			&log.ClientIP,
			&log.ConnectedAt,
			&log.DisconnectedAt,
			&log.BytesReceived,
			&log.BytesSent,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	return logs, nil
}

func (r *LogRepository) ListByClient(clientName string, limit int) ([]*models.ConnectionLog, error) {
	query := `
		SELECT id, client_name, client_ip, connected_at, disconnected_at, bytes_received, bytes_sent
		FROM connection_logs
		WHERE client_name = ?
		ORDER BY connected_at DESC
		LIMIT ?
	`
	
	rows, err := r.db.Query(query, clientName, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var logs []*models.ConnectionLog
	for rows.Next() {
		log := &models.ConnectionLog{}
		err := rows.Scan(
			&log.ID,
			&log.ClientName,
			&log.ClientIP,
			&log.ConnectedAt,
			&log.DisconnectedAt,
			&log.BytesReceived,
			&log.BytesSent,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	return logs, nil
}

func (r *LogRepository) GetActiveConnections() ([]*models.ConnectionLog, error) {
	query := `
		SELECT id, client_name, client_ip, connected_at, disconnected_at, bytes_received, bytes_sent
		FROM connection_logs
		WHERE disconnected_at IS NULL
		ORDER BY connected_at DESC
	`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var logs []*models.ConnectionLog
	for rows.Next() {
		log := &models.ConnectionLog{}
		err := rows.Scan(
			&log.ID,
			&log.ClientName,
			&log.ClientIP,
			&log.ConnectedAt,
			&log.DisconnectedAt,
			&log.BytesReceived,
			&log.BytesSent,
		)
		if err != nil {
			return nil, err
		}
		logs = append(logs, log)
	}

	return logs, nil
}

func (r *LogRepository) UpdateDisconnection(id int, disconnectedAt time.Time, bytesReceived, bytesSent int64) error {
	query := `
		UPDATE connection_logs
		SET disconnected_at = ?, bytes_received = ?, bytes_sent = ?
		WHERE id = ?
	`
	
	_, err := r.db.Exec(query, disconnectedAt, bytesReceived, bytesSent, id)
	return err
}

func (r *LogRepository) GetStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total connections
	var totalConnections int
	err := r.db.QueryRow("SELECT COUNT(*) FROM connection_logs").Scan(&totalConnections)
	if err != nil {
		return nil, err
	}
	stats["total_connections"] = totalConnections

	// Active connections
	var activeConnections int
	err = r.db.QueryRow("SELECT COUNT(*) FROM connection_logs WHERE disconnected_at IS NULL").Scan(&activeConnections)
	if err != nil {
		return nil, err
	}
	stats["active_connections"] = activeConnections

	// Total data transfer
	var totalBytesReceived, totalBytesSent sql.NullInt64
	err = r.db.QueryRow("SELECT SUM(bytes_received), SUM(bytes_sent) FROM connection_logs").Scan(&totalBytesReceived, &totalBytesSent)
	if err != nil {
		return nil, err
	}
	
	stats["total_bytes_received"] = totalBytesReceived.Int64
	stats["total_bytes_sent"] = totalBytesSent.Int64

	// Connections today
	var connectionsToday int
	err = r.db.QueryRow("SELECT COUNT(*) FROM connection_logs WHERE DATE(connected_at) = DATE('now')").Scan(&connectionsToday)
	if err != nil {
		return nil, err
	}
	stats["connections_today"] = connectionsToday

	return stats, nil
}

func (r *LogRepository) CleanupOldLogs(days int) error {
	query := `
		DELETE FROM connection_logs
		WHERE connected_at < datetime('now', '-' || ? || ' days')
		AND disconnected_at IS NOT NULL
	`
	
	_, err := r.db.Exec(query, days)
	return err
}
