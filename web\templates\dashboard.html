{{define "content"}}
<div class="row">
    <div class="col-12">
        <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="server-status">Checking...</h4>
                        <p class="mb-0">Server Status</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-server fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="connected-clients">0</h4>
                        <p class="mb-0">Connected Clients</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="total-clients">0</h4>
                        <p class="mb-0">Total Clients</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-certificate fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="data-transfer">0 MB</h4>
                        <p class="mb-0">Data Transfer</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exchange-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-cogs"></i> Server Control</h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-success btn-sm" id="start-server">
                        <i class="fas fa-play"></i> Start
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" id="restart-server">
                        <i class="fas fa-redo"></i> Restart
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" id="stop-server">
                        <i class="fas fa-stop"></i> Stop
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>Status:</strong> <span id="detailed-status" class="badge bg-secondary">Unknown</span>
                </div>
                <div class="mb-3">
                    <strong>Uptime:</strong> <span id="uptime">-</span>
                </div>
                <div class="mb-3">
                    <strong>Port:</strong> <span id="server-port">1194</span>
                </div>
                <div class="mb-3">
                    <strong>Protocol:</strong> <span id="server-protocol">UDP</span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-users"></i> Active Connections</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Client</th>
                                <th>IP Address</th>
                                <th>Connected</th>
                            </tr>
                        </thead>
                        <tbody id="active-connections">
                            <tr>
                                <td colspan="3" class="text-center text-muted">No active connections</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-file-alt"></i> Recent Logs</h5>
                <button type="button" class="btn btn-outline-primary btn-sm" id="refresh-logs">
                    <i class="fas fa-sync"></i> Refresh
                </button>
            </div>
            <div class="card-body">
                <div id="log-container" style="height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                    <div class="text-muted">Loading logs...</div>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}
