package database

import (
	"database/sql"
	"time"
	"vpn-manager/internal/models"
)

type ConfigRepository struct {
	db *sql.DB
}

func NewConfigRepository(db *sql.DB) *ConfigRepository {
	return &ConfigRepository{db: db}
}

func (r *ConfigRepository) Get() (*models.ServerConfig, error) {
	query := `
		SELECT id, port, protocol, cipher, auth, compression, network, netmask,
		       dns1, dns2, redirect_gateway, duplicate_cn, client_to_client,
		       max_clients, keep_alive, created_at, updated_at
		FROM server_config
		WHERE id = 1
	`
	
	config := &models.ServerConfig{}
	err := r.db.QueryRow(query).Scan(
		&config.ID,
		&config.Port,
		&config.Protocol,
		&config.Cipher,
		&config.Auth,
		&config.Compression,
		&config.Network,
		&config.Netmask,
		&config.DNS1,
		&config.DNS2,
		&config.RedirectGateway,
		&config.DuplicateCN,
		&config.ClientToClient,
		&config.MaxClients,
		&config.KeepAlive,
		&config.CreatedAt,
		&config.UpdatedAt,
	)
	
	if err != nil {
		return nil, err
	}
	
	return config, nil
}

func (r *ConfigRepository) Update(config *models.ServerConfig) error {
	query := `
		UPDATE server_config
		SET port = ?, protocol = ?, cipher = ?, auth = ?, compression = ?,
		    network = ?, netmask = ?, dns1 = ?, dns2 = ?, redirect_gateway = ?,
		    duplicate_cn = ?, client_to_client = ?, max_clients = ?, keep_alive = ?,
		    updated_at = ?
		WHERE id = 1
	`
	
	now := time.Now()
	_, err := r.db.Exec(query,
		config.Port,
		config.Protocol,
		config.Cipher,
		config.Auth,
		config.Compression,
		config.Network,
		config.Netmask,
		config.DNS1,
		config.DNS2,
		config.RedirectGateway,
		config.DuplicateCN,
		config.ClientToClient,
		config.MaxClients,
		config.KeepAlive,
		now,
	)
	
	if err != nil {
		return err
	}

	config.UpdatedAt = now
	return nil
}

func (r *ConfigRepository) Reset() error {
	query := `
		UPDATE server_config
		SET port = 1194,
		    protocol = 'udp',
		    cipher = 'AES-256-GCM',
		    auth = 'SHA256',
		    compression = 'lz4-v2',
		    network = '********',
		    netmask = '*************',
		    dns1 = '*******',
		    dns2 = '*******',
		    redirect_gateway = 1,
		    duplicate_cn = 0,
		    client_to_client = 0,
		    max_clients = 100,
		    keep_alive = '10 120',
		    updated_at = ?
		WHERE id = 1
	`
	
	_, err := r.db.Exec(query, time.Now())
	return err
}
