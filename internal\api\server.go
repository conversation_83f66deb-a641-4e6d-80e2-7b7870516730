package api

import (
	"database/sql"
	"fmt"
	"net/http"
	"time"

	"vpn-manager/internal/auth"
	"vpn-manager/internal/config"
	"vpn-manager/internal/database"
	"vpn-manager/internal/openvpn"

	"github.com/gin-gonic/gin"
)

type Server struct {
	config       *config.Config
	db           *sql.DB
	router       *gin.Engine
	handlers     *Handlers
	authHandlers *auth.AuthHandlers
	vpnService   *openvpn.Service
}

func NewServer(cfg *config.Config, db *sql.DB) *Server {
	// Initialize repositories
	repos := database.NewRepositories(db)

	// Create default admin user if it doesn't exist
	if err := repos.Users.CreateDefaultAdmin(cfg.AdminUsername, cfg.AdminPassword); err != nil {
		fmt.Printf("Warning: Failed to create default admin user: %v\n", err)
	}

	// Initialize JWT manager
	jwtManager := auth.NewJWTManager(cfg.JWTSecret, 24*time.Hour)

	// Initialize auth handlers
	authHandlers := auth.NewAuthHandlers(repos.Users, jwtManager)

	// Initialize VPN service
	vpnService := openvpn.NewService(cfg)
	if err := vpnService.Initialize(); err != nil {
		// Log error but don't fail - service might work without full initialization
		fmt.Printf("Warning: Failed to initialize VPN service: %v\n", err)
	}

	// Initialize handlers
	handlers := NewHandlers(repos, vpnService)

	server := &Server{
		config:       cfg,
		db:           db,
		handlers:     handlers,
		authHandlers: authHandlers,
		vpnService:   vpnService,
	}

	server.setupRoutes()
	return server
}

func (s *Server) setupRoutes() {
	if s.config.LogLevel == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	s.router = gin.Default()

	// Serve static files
	s.router.Static("/static", "./web/static")
	s.router.LoadHTMLGlob("web/templates/*")

	// Web interface routes
	s.router.GET("/", s.handleDashboard)
	s.router.GET("/clients", s.handleClientsPage)
	s.router.GET("/config", s.handleConfigPage)
	s.router.GET("/logs", s.handleLogsPage)
	s.router.GET("/login", s.handleLogin)
	s.router.POST("/login", s.handleLoginPost)
	s.router.GET("/logout", s.handleLogout)

	// API routes
	api := s.router.Group("/api/v1")
	{
		// Authentication
		api.POST("/auth/login", s.authHandlers.HandleLogin)
		api.POST("/auth/refresh", s.authHandlers.HandleRefreshToken)
		api.POST("/auth/logout", s.authHandlers.HandleLogout)

		// Protected routes
		protected := api.Group("/")
		protected.Use(s.authHandlers.AuthMiddleware())
		{
			// User profile
			protected.GET("/profile", s.authHandlers.HandleGetProfile)
			protected.POST("/profile/password", s.authHandlers.HandleChangePassword)
			// Server management (admin only)
			serverRoutes := protected.Group("/server")
			serverRoutes.Use(s.authHandlers.RequirePermission("control_server"))
			{
				serverRoutes.GET("/status", s.handlers.HandleServerStatus)
				serverRoutes.POST("/start", s.handlers.HandleServerStart)
				serverRoutes.POST("/stop", s.handlers.HandleServerStop)
				serverRoutes.POST("/restart", s.handlers.HandleServerRestart)
			}

			// Configuration (admin only)
			configRoutes := protected.Group("/config")
			configRoutes.Use(s.authHandlers.RequirePermission("admin"))
			{
				configRoutes.GET("", s.handlers.HandleGetConfig)
				configRoutes.PUT("", s.handlers.HandleUpdateConfig)
			}

			// Client management (admin and manager)
			clientRoutes := protected.Group("/clients")
			clientRoutes.Use(s.authHandlers.RequirePermission("manage_clients"))
			{
				clientRoutes.GET("", s.handlers.HandleGetClients)
				clientRoutes.POST("", s.handlers.HandleCreateClient)
				clientRoutes.DELETE("/:id", s.handlers.HandleDeleteClient)
				clientRoutes.GET("/:id/config", s.handlers.HandleDownloadClientConfig)
				clientRoutes.POST("/:id/revoke", s.handlers.HandleRevokeClient)
			}

			// Monitoring (all authenticated users can view)
			monitorRoutes := protected.Group("/")
			monitorRoutes.Use(s.authHandlers.RequirePermission("view_logs"))
			{
				monitorRoutes.GET("/status", s.handlers.HandleGetStatus)
				monitorRoutes.GET("/logs", s.handlers.HandleGetLogs)
				monitorRoutes.GET("/connections", s.handlers.HandleGetConnections)
			}
		}
	}
}

func (s *Server) Start() error {
	addr := fmt.Sprintf(":%s", s.config.ServerPort)
	return s.router.Run(addr)
}

// Placeholder handlers - will be implemented in subsequent tasks
func (s *Server) handleDashboard(c *gin.Context) {
	c.HTML(http.StatusOK, "dashboard.html", gin.H{
		"title": "VPN Manager Dashboard",
	})
}

func (s *Server) handleClientsPage(c *gin.Context) {
	c.HTML(http.StatusOK, "clients.html", gin.H{
		"title": "Client Management",
	})
}

func (s *Server) handleConfigPage(c *gin.Context) {
	c.HTML(http.StatusOK, "config.html", gin.H{
		"title": "Server Configuration",
	})
}

func (s *Server) handleLogsPage(c *gin.Context) {
	c.HTML(http.StatusOK, "logs.html", gin.H{
		"title": "Server Logs",
	})
}

func (s *Server) handleLogin(c *gin.Context) {
	c.HTML(http.StatusOK, "login.html", gin.H{
		"title": "Login",
	})
}

func (s *Server) handleLoginPost(c *gin.Context) {
	// TODO: Implement login logic
	c.Redirect(http.StatusFound, "/")
}

func (s *Server) handleLogout(c *gin.Context) {
	// TODO: Implement logout logic
	c.Redirect(http.StatusFound, "/login")
}

// Additional server methods can be added here if needed
