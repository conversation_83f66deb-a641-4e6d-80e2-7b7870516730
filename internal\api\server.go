package api

import (
	"database/sql"
	"fmt"
	"net/http"

	"vpn-manager/internal/config"
	"vpn-manager/internal/database"
	"vpn-manager/internal/openvpn"

	"github.com/gin-gonic/gin"
)

type Server struct {
	config     *config.Config
	db         *sql.DB
	router     *gin.Engine
	handlers   *Handlers
	vpnService *openvpn.Service
}

func NewServer(cfg *config.Config, db *sql.DB) *Server {
	// Initialize repositories
	repos := database.NewRepositories(db)

	// Initialize VPN service
	vpnService := openvpn.NewService(cfg)
	if err := vpnService.Initialize(); err != nil {
		// Log error but don't fail - service might work without full initialization
		fmt.Printf("Warning: Failed to initialize VPN service: %v\n", err)
	}

	// Initialize handlers
	handlers := NewHandlers(repos, vpnService)

	server := &Server{
		config:     cfg,
		db:         db,
		handlers:   handlers,
		vpnService: vpnService,
	}

	server.setupRoutes()
	return server
}

func (s *Server) setupRoutes() {
	if s.config.LogLevel == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	s.router = gin.Default()

	// Serve static files
	s.router.Static("/static", "./web/static")
	s.router.LoadHTMLGlob("web/templates/*")

	// Web interface routes
	s.router.GET("/", s.handleDashboard)
	s.router.GET("/login", s.handleLogin)
	s.router.POST("/login", s.handleLoginPost)
	s.router.GET("/logout", s.handleLogout)

	// API routes
	api := s.router.Group("/api/v1")
	{
		// Authentication
		api.POST("/auth/login", s.handleAPILogin)

		// Protected routes
		protected := api.Group("/")
		protected.Use(s.authMiddleware())
		{
			// Server management
			protected.GET("/server/status", s.handlers.HandleServerStatus)
			protected.POST("/server/start", s.handlers.HandleServerStart)
			protected.POST("/server/stop", s.handlers.HandleServerStop)
			protected.POST("/server/restart", s.handlers.HandleServerRestart)

			// Configuration
			protected.GET("/config", s.handlers.HandleGetConfig)
			protected.PUT("/config", s.handlers.HandleUpdateConfig)

			// Client management
			protected.GET("/clients", s.handlers.HandleGetClients)
			protected.POST("/clients", s.handlers.HandleCreateClient)
			protected.DELETE("/clients/:id", s.handlers.HandleDeleteClient)
			protected.GET("/clients/:id/config", s.handlers.HandleDownloadClientConfig)
			protected.POST("/clients/:id/revoke", s.handlers.HandleRevokeClient)

			// Monitoring
			protected.GET("/status", s.handlers.HandleGetStatus)
			protected.GET("/logs", s.handlers.HandleGetLogs)
			protected.GET("/connections", s.handlers.HandleGetConnections)
		}
	}
}

func (s *Server) Start() error {
	addr := fmt.Sprintf(":%s", s.config.ServerPort)
	return s.router.Run(addr)
}

// Placeholder handlers - will be implemented in subsequent tasks
func (s *Server) handleDashboard(c *gin.Context) {
	c.HTML(http.StatusOK, "dashboard.html", gin.H{
		"title": "VPN Manager Dashboard",
	})
}

func (s *Server) handleLogin(c *gin.Context) {
	c.HTML(http.StatusOK, "login.html", gin.H{
		"title": "Login",
	})
}

func (s *Server) handleLoginPost(c *gin.Context) {
	// TODO: Implement login logic
	c.Redirect(http.StatusFound, "/")
}

func (s *Server) handleLogout(c *gin.Context) {
	// TODO: Implement logout logic
	c.Redirect(http.StatusFound, "/login")
}

func (s *Server) handleAPILogin(c *gin.Context) {
	// TODO: Implement API login
	c.JSON(http.StatusOK, gin.H{"message": "Login endpoint"})
}

func (s *Server) authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: Implement JWT authentication
		c.Next()
	}
}

// Additional server methods can be added here if needed
