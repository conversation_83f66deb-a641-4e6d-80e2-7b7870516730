package models

import (
	"time"
)

// User represents a system user
type User struct {
	ID        int       `json:"id" db:"id"`
	Userna<PERSON>  string    `json:"username" db:"username"`
	Password  string    `json:"-" db:"password"` // Never expose password in JSON
	Role      string    `json:"role" db:"role"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// VPNClient represents a VPN client configuration
type VPNClient struct {
	ID          int       `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	CommonName  string    `json:"common_name" db:"common_name"`
	Email       string    `json:"email" db:"email"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	RevokedAt   *time.Time `json:"revoked_at,omitempty" db:"revoked_at"`
}

// ServerConfig represents OpenVPN server configuration
type ServerConfig struct {
	ID              int    `json:"id" db:"id"`
	Port            int    `json:"port" db:"port"`
	Protocol        string `json:"protocol" db:"protocol"`
	Cipher          string `json:"cipher" db:"cipher"`
	Auth            string `json:"auth" db:"auth"`
	Compression     string `json:"compression" db:"compression"`
	Network         string `json:"network" db:"network"`
	Netmask         string `json:"netmask" db:"netmask"`
	DNS1            string `json:"dns1" db:"dns1"`
	DNS2            string `json:"dns2" db:"dns2"`
	RedirectGateway bool   `json:"redirect_gateway" db:"redirect_gateway"`
	DuplicateCN     bool   `json:"duplicate_cn" db:"duplicate_cn"`
	ClientToClient  bool   `json:"client_to_client" db:"client_to_client"`
	MaxClients      int    `json:"max_clients" db:"max_clients"`
	KeepAlive       string `json:"keep_alive" db:"keep_alive"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time `json:"updated_at" db:"updated_at"`
}

// ConnectionLog represents a client connection log entry
type ConnectionLog struct {
	ID         int       `json:"id" db:"id"`
	ClientName string    `json:"client_name" db:"client_name"`
	ClientIP   string    `json:"client_ip" db:"client_ip"`
	ConnectedAt time.Time `json:"connected_at" db:"connected_at"`
	DisconnectedAt *time.Time `json:"disconnected_at,omitempty" db:"disconnected_at"`
	BytesReceived  int64     `json:"bytes_received" db:"bytes_received"`
	BytesSent      int64     `json:"bytes_sent" db:"bytes_sent"`
}

// ServerStatus represents current server status
type ServerStatus struct {
	IsRunning      bool      `json:"is_running"`
	PID            int       `json:"pid,omitempty"`
	StartTime      time.Time `json:"start_time,omitempty"`
	ConnectedClients int     `json:"connected_clients"`
	TotalConnections int     `json:"total_connections"`
	BytesReceived    int64   `json:"bytes_received"`
	BytesSent        int64   `json:"bytes_sent"`
}

// ActiveClient represents a currently connected client
type ActiveClient struct {
	CommonName    string    `json:"common_name"`
	RealAddress   string    `json:"real_address"`
	VirtualAddress string   `json:"virtual_address"`
	ConnectedSince time.Time `json:"connected_since"`
	BytesReceived  int64     `json:"bytes_received"`
	BytesSent      int64     `json:"bytes_sent"`
}
