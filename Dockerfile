# Multi-stage build for VPN Manager
FROM golang:1.21-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git gcc musl-dev sqlite-dev

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o vpn-manager ./cmd/server

# Production stage
FROM alpine:3.18

# Install runtime dependencies
RUN apk add --no-cache \
    openvpn \
    easy-rsa \
    iptables \
    openssl \
    ca-certificates \
    sqlite \
    bash \
    curl \
    && rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1000 vpnmanager && \
    adduser -D -s /bin/bash -u 1000 -G vpnmanager vpnmanager

# Create necessary directories
RUN mkdir -p /app/data/openvpn \
             /app/data/certs \
             /app/data/logs \
             /app/web/static \
             /app/web/templates \
             /etc/openvpn/server \
             /var/log/openvpn && \
    chown -R vpnmanager:vpnmanager /app /etc/openvpn /var/log/openvpn

# Copy built application
COPY --from=builder /app/vpn-manager /app/vpn-manager

# Copy web assets
COPY --chown=vpnmanager:vpnmanager web/ /app/web/

# Copy configuration template
COPY --chown=vpnmanager:vpnmanager config.json.example /app/config.json.example

# Create startup script
RUN cat > /app/start.sh << 'EOF'
#!/bin/bash
set -e

# Initialize Easy-RSA if not already done
if [ ! -f /app/data/certs/ca.crt ]; then
    echo "Initializing PKI..."
    cd /app/data/certs
    
    # Initialize Easy-RSA
    /usr/share/easy-rsa/easyrsa init-pki
    
    # Build CA
    echo "vpn-manager-ca" | /usr/share/easy-rsa/easyrsa build-ca nopass
    
    # Generate server certificate
    echo "server" | /usr/share/easy-rsa/easyrsa gen-req server nopass
    echo "yes" | /usr/share/easy-rsa/easyrsa sign-req server server
    
    # Generate DH parameters
    /usr/share/easy-rsa/easyrsa gen-dh
    
    # Generate TLS auth key
    openvpn --genkey --secret ta.key
    
    # Copy certificates to expected locations
    cp pki/ca.crt ca.crt
    cp pki/issued/server.crt server.crt
    cp pki/private/server.key server.key
    cp pki/dh.pem dh.pem
    
    echo "PKI initialization complete"
fi

# Set proper permissions
chown -R vpnmanager:vpnmanager /app/data

# Create default config if not exists
if [ ! -f /app/config.json ]; then
    cp /app/config.json.example /app/config.json
fi

# Start the VPN Manager
exec /app/vpn-manager
EOF

RUN chmod +x /app/start.sh && chown vpnmanager:vpnmanager /app/start.sh

# Switch to non-root user
USER vpnmanager

# Set working directory
WORKDIR /app

# Expose ports
EXPOSE 8080 1194/udp

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/api/v1/server/status || exit 1

# Set environment variables
ENV SERVER_PORT=8080 \
    DATABASE_PATH=/app/data/vpn-manager.db \
    OPENVPN_PATH=/usr/sbin/openvpn \
    CONFIG_DIR=/app/data/openvpn \
    CERT_DIR=/app/data/certs \
    LOG_LEVEL=info

# Start the application
CMD ["/app/start.sh"]
