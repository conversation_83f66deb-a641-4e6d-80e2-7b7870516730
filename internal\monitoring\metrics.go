package monitoring

import (
	"runtime"
	"sync"
	"time"
)

type Metrics struct {
	mu                sync.RWMutex
	startTime         time.Time
	requestCount      int64
	errorCount        int64
	authAttempts      int64
	failedAuthAttempts int64
	clientConnections int64
	activeClients     int64
	dataTransferred   int64
	lastActivity      time.Time
}

func NewMetrics() *Metrics {
	return &Metrics{
		startTime:    time.Now(),
		lastActivity: time.Now(),
	}
}

func (m *Metrics) IncrementRequests() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.requestCount++
	m.lastActivity = time.Now()
}

func (m *Metrics) IncrementErrors() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.errorCount++
}

func (m *Metrics) IncrementAuthAttempts(success bool) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.authAttempts++
	if !success {
		m.failedAuthAttempts++
	}
}

func (m *Metrics) IncrementClientConnections() {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.clientConnections++
	m.activeClients++
}

func (m *Metrics) DecrementActiveClients() {
	m.mu.Lock()
	defer m.mu.Unlock()
	if m.activeClients > 0 {
		m.activeClients--
	}
}

func (m *Metrics) AddDataTransferred(bytes int64) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.dataTransferred += bytes
}

func (m *Metrics) GetSnapshot() MetricsSnapshot {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	return MetricsSnapshot{
		StartTime:          m.startTime,
		Uptime:             time.Since(m.startTime),
		RequestCount:       m.requestCount,
		ErrorCount:         m.errorCount,
		AuthAttempts:       m.authAttempts,
		FailedAuthAttempts: m.failedAuthAttempts,
		ClientConnections:  m.clientConnections,
		ActiveClients:      m.activeClients,
		DataTransferred:    m.dataTransferred,
		LastActivity:       m.lastActivity,
		MemoryUsage:        memStats.Alloc,
		MemoryTotal:        memStats.TotalAlloc,
		GoroutineCount:     runtime.NumGoroutine(),
		CPUCount:           runtime.NumCPU(),
	}
}

type MetricsSnapshot struct {
	StartTime          time.Time `json:"start_time"`
	Uptime             time.Duration `json:"uptime"`
	RequestCount       int64     `json:"request_count"`
	ErrorCount         int64     `json:"error_count"`
	AuthAttempts       int64     `json:"auth_attempts"`
	FailedAuthAttempts int64     `json:"failed_auth_attempts"`
	ClientConnections  int64     `json:"client_connections"`
	ActiveClients      int64     `json:"active_clients"`
	DataTransferred    int64     `json:"data_transferred"`
	LastActivity       time.Time `json:"last_activity"`
	MemoryUsage        uint64    `json:"memory_usage"`
	MemoryTotal        uint64    `json:"memory_total"`
	GoroutineCount     int       `json:"goroutine_count"`
	CPUCount           int       `json:"cpu_count"`
}

// Health check functionality
type HealthChecker struct {
	checks map[string]HealthCheck
	mu     sync.RWMutex
}

type HealthCheck struct {
	Name        string    `json:"name"`
	Status      string    `json:"status"`
	Message     string    `json:"message"`
	LastChecked time.Time `json:"last_checked"`
}

type HealthStatus struct {
	Overall string                 `json:"overall"`
	Checks  map[string]HealthCheck `json:"checks"`
}

func NewHealthChecker() *HealthChecker {
	return &HealthChecker{
		checks: make(map[string]HealthCheck),
	}
}

func (h *HealthChecker) RegisterCheck(name string, checkFunc func() (bool, string)) {
	go func() {
		ticker := time.NewTicker(30 * time.Second) // Check every 30 seconds
		defer ticker.Stop()

		for {
			healthy, message := checkFunc()
			status := "healthy"
			if !healthy {
				status = "unhealthy"
			}

			h.mu.Lock()
			h.checks[name] = HealthCheck{
				Name:        name,
				Status:      status,
				Message:     message,
				LastChecked: time.Now(),
			}
			h.mu.Unlock()

			<-ticker.C
		}
	}()
}

func (h *HealthChecker) GetStatus() HealthStatus {
	h.mu.RLock()
	defer h.mu.RUnlock()

	overall := "healthy"
	for _, check := range h.checks {
		if check.Status != "healthy" {
			overall = "unhealthy"
			break
		}
	}

	return HealthStatus{
		Overall: overall,
		Checks:  h.checks,
	}
}

// Alert system
type AlertLevel int

const (
	INFO AlertLevel = iota
	WARNING
	CRITICAL
)

func (a AlertLevel) String() string {
	switch a {
	case INFO:
		return "INFO"
	case WARNING:
		return "WARNING"
	case CRITICAL:
		return "CRITICAL"
	default:
		return "UNKNOWN"
	}
}

type Alert struct {
	ID        string     `json:"id"`
	Level     AlertLevel `json:"level"`
	Title     string     `json:"title"`
	Message   string     `json:"message"`
	Timestamp time.Time  `json:"timestamp"`
	Resolved  bool       `json:"resolved"`
}

type AlertManager struct {
	alerts []Alert
	mu     sync.RWMutex
}

func NewAlertManager() *AlertManager {
	return &AlertManager{
		alerts: make([]Alert, 0),
	}
}

func (am *AlertManager) AddAlert(level AlertLevel, title, message string) {
	am.mu.Lock()
	defer am.mu.Unlock()

	alert := Alert{
		ID:        generateAlertID(),
		Level:     level,
		Title:     title,
		Message:   message,
		Timestamp: time.Now(),
		Resolved:  false,
	}

	am.alerts = append(am.alerts, alert)

	// Keep only last 100 alerts
	if len(am.alerts) > 100 {
		am.alerts = am.alerts[len(am.alerts)-100:]
	}
}

func (am *AlertManager) ResolveAlert(id string) {
	am.mu.Lock()
	defer am.mu.Unlock()

	for i := range am.alerts {
		if am.alerts[i].ID == id {
			am.alerts[i].Resolved = true
			break
		}
	}
}

func (am *AlertManager) GetAlerts(resolved bool) []Alert {
	am.mu.RLock()
	defer am.mu.RUnlock()

	var filteredAlerts []Alert
	for _, alert := range am.alerts {
		if alert.Resolved == resolved {
			filteredAlerts = append(filteredAlerts, alert)
		}
	}

	return filteredAlerts
}

func generateAlertID() string {
	return time.Now().Format("20060102150405") + "-" + time.Now().Format("000")
}

// Global instances
var (
	globalMetrics      *Metrics
	globalHealthChecker *HealthChecker
	globalAlertManager *AlertManager
)

func InitGlobalMonitoring() {
	globalMetrics = NewMetrics()
	globalHealthChecker = NewHealthChecker()
	globalAlertManager = NewAlertManager()
}

func GetMetrics() *Metrics {
	return globalMetrics
}

func GetHealthChecker() *HealthChecker {
	return globalHealthChecker
}

func GetAlertManager() *AlertManager {
	return globalAlertManager
}

// Convenience functions
func IncrementRequests() {
	if globalMetrics != nil {
		globalMetrics.IncrementRequests()
	}
}

func IncrementErrors() {
	if globalMetrics != nil {
		globalMetrics.IncrementErrors()
	}
}

func IncrementAuthAttempts(success bool) {
	if globalMetrics != nil {
		globalMetrics.IncrementAuthAttempts(success)
	}
}

func AddAlert(level AlertLevel, title, message string) {
	if globalAlertManager != nil {
		globalAlertManager.AddAlert(level, title, message)
	}
}
