package logging

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"time"
)

type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARNING
	ERROR
)

func (l LogLevel) String() string {
	switch l {
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARNING:
		return "WARNING"
	case ERROR:
		return "ERROR"
	default:
		return "UNKNOWN"
	}
}

type Logger struct {
	level      LogLevel
	fileLogger *log.Logger
	consoleLogger *log.Logger
	logFile    *os.File
}

func NewLogger(level LogLevel, logFilePath string) (*Logger, error) {
	// Create log directory if it doesn't exist
	logDir := filepath.Dir(logFilePath)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %v", err)
	}

	// Open log file
	logFile, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return nil, fmt.Errorf("failed to open log file: %v", err)
	}

	// Create multi-writer for both file and console
	multiWriter := io.MultiWriter(logFile, os.Stdout)

	logger := &Logger{
		level:         level,
		fileLogger:    log.New(logFile, "", log.LstdFlags),
		consoleLogger: log.New(multiWriter, "", log.LstdFlags),
		logFile:       logFile,
	}

	return logger, nil
}

func (l *Logger) Close() error {
	if l.logFile != nil {
		return l.logFile.Close()
	}
	return nil
}

func (l *Logger) log(level LogLevel, format string, args ...interface{}) {
	if level < l.level {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	message := fmt.Sprintf(format, args...)
	logEntry := fmt.Sprintf("[%s] [%s] %s", timestamp, level.String(), message)

	// Log to file
	l.fileLogger.Println(logEntry)

	// Log to console only for INFO and above
	if level >= INFO {
		l.consoleLogger.Println(logEntry)
	}
}

func (l *Logger) Debug(format string, args ...interface{}) {
	l.log(DEBUG, format, args...)
}

func (l *Logger) Info(format string, args ...interface{}) {
	l.log(INFO, format, args...)
}

func (l *Logger) Warning(format string, args ...interface{}) {
	l.log(WARNING, format, args...)
}

func (l *Logger) Error(format string, args ...interface{}) {
	l.log(ERROR, format, args...)
}

// Convenience methods for common logging scenarios
func (l *Logger) LogServerStart(port string) {
	l.Info("VPN Manager server starting on port %s", port)
}

func (l *Logger) LogServerStop() {
	l.Info("VPN Manager server stopping")
}

func (l *Logger) LogClientConnect(clientName, clientIP string) {
	l.Info("Client connected: %s from %s", clientName, clientIP)
}

func (l *Logger) LogClientDisconnect(clientName, clientIP string, duration time.Duration) {
	l.Info("Client disconnected: %s from %s (connected for %v)", clientName, clientIP, duration)
}

func (l *Logger) LogConfigChange(user, change string) {
	l.Info("Configuration changed by %s: %s", user, change)
}

func (l *Logger) LogAuthAttempt(username, ip string, success bool) {
	if success {
		l.Info("Successful authentication: %s from %s", username, ip)
	} else {
		l.Warning("Failed authentication attempt: %s from %s", username, ip)
	}
}

func (l *Logger) LogAPIRequest(method, path, user, ip string, statusCode int, duration time.Duration) {
	l.Info("API %s %s - User: %s, IP: %s, Status: %d, Duration: %v", 
		method, path, user, ip, statusCode, duration)
}

func (l *Logger) LogVPNServerAction(action, user string, success bool) {
	if success {
		l.Info("VPN server %s by %s - SUCCESS", action, user)
	} else {
		l.Error("VPN server %s by %s - FAILED", action, user)
	}
}

func (l *Logger) LogCertificateAction(action, clientName, user string) {
	l.Info("Certificate %s for client %s by user %s", action, clientName, user)
}

// Global logger instance
var globalLogger *Logger

func InitGlobalLogger(level LogLevel, logFilePath string) error {
	var err error
	globalLogger, err = NewLogger(level, logFilePath)
	return err
}

func GetGlobalLogger() *Logger {
	return globalLogger
}

func CloseGlobalLogger() error {
	if globalLogger != nil {
		return globalLogger.Close()
	}
	return nil
}

// Global convenience functions
func Debug(format string, args ...interface{}) {
	if globalLogger != nil {
		globalLogger.Debug(format, args...)
	}
}

func Info(format string, args ...interface{}) {
	if globalLogger != nil {
		globalLogger.Info(format, args...)
	}
}

func Warning(format string, args ...interface{}) {
	if globalLogger != nil {
		globalLogger.Warning(format, args...)
	}
}

func Error(format string, args ...interface{}) {
	if globalLogger != nil {
		globalLogger.Error(format, args...)
	}
}

// Log rotation functionality
func (l *Logger) RotateLog() error {
	// Close current log file
	if l.logFile != nil {
		l.logFile.Close()
	}

	// Rename current log file with timestamp
	logPath := l.logFile.Name()
	timestamp := time.Now().Format("20060102-150405")
	rotatedPath := fmt.Sprintf("%s.%s", logPath, timestamp)
	
	if err := os.Rename(logPath, rotatedPath); err != nil {
		return fmt.Errorf("failed to rotate log file: %v", err)
	}

	// Create new log file
	newLogFile, err := os.OpenFile(logPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("failed to create new log file: %v", err)
	}

	l.logFile = newLogFile
	l.fileLogger = log.New(newLogFile, "", log.LstdFlags)

	l.Info("Log file rotated. Previous log saved as: %s", rotatedPath)
	return nil
}

// Cleanup old log files (keep only last N files)
func CleanupOldLogs(logDir string, keepCount int) error {
	files, err := filepath.Glob(filepath.Join(logDir, "*.log.*"))
	if err != nil {
		return err
	}

	if len(files) <= keepCount {
		return nil
	}

	// Sort files by modification time (oldest first)
	// This is a simplified approach - in production you might want more sophisticated sorting
	for i := 0; i < len(files)-keepCount; i++ {
		if err := os.Remove(files[i]); err != nil {
			return fmt.Errorf("failed to remove old log file %s: %v", files[i], err)
		}
	}

	return nil
}

// Parse log level from string
func ParseLogLevel(levelStr string) LogLevel {
	switch levelStr {
	case "debug":
		return DEBUG
	case "info":
		return INFO
	case "warning":
		return WARNING
	case "error":
		return ERROR
	default:
		return INFO
	}
}
