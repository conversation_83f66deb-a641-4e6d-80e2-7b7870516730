package api

import (
	"fmt"
	"net/http"
	"strconv"

	"vpn-manager/internal/database"
	"vpn-manager/internal/models"
	"vpn-manager/internal/openvpn"

	"github.com/gin-gonic/gin"
)

type Handlers struct {
	repos         *database.Repositories
	vpnService    *openvpn.Service
	clientManager *openvpn.ClientManager
	monitor       *openvpn.Monitor
}

func NewHandlers(repos *database.Repositories, vpnService *openvpn.Service) *Handlers {
	return &Handlers{
		repos:         repos,
		vpnService:    vpnService,
		clientManager: openvpn.NewClientManager(vpnService),
		monitor:       openvpn.NewMonitor(vpnService),
	}
}

// Server Management Handlers

func (h *Handlers) HandleServerStatus(c *gin.Context) {
	status, err := h.vpnService.GetStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, status)
}

func (h *Handlers) HandleServerStart(c *gin.Context) {
	// Get current server configuration
	config, err := h.repos.Config.Get()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get server configuration"})
		return
	}

	// Generate server configuration file
	if err := h.vpnService.GenerateServerConfig(config); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate server configuration"})
		return
	}

	// Start the server
	if err := h.vpnService.Start(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Server started successfully"})
}

func (h *Handlers) HandleServerStop(c *gin.Context) {
	if err := h.vpnService.Stop(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Server stopped successfully"})
}

func (h *Handlers) HandleServerRestart(c *gin.Context) {
	// Get current server configuration
	config, err := h.repos.Config.Get()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get server configuration"})
		return
	}

	// Generate server configuration file
	if err := h.vpnService.GenerateServerConfig(config); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate server configuration"})
		return
	}

	// Restart the server
	if err := h.vpnService.Restart(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Server restarted successfully"})
}

// Configuration Handlers

func (h *Handlers) HandleGetConfig(c *gin.Context) {
	config, err := h.repos.Config.Get()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get configuration"})
		return
	}

	c.JSON(http.StatusOK, config)
}

func (h *Handlers) HandleUpdateConfig(c *gin.Context) {
	var config models.ServerConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate configuration
	if err := h.validateServerConfig(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update configuration in database
	if err := h.repos.Config.Update(&config); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update configuration"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Configuration updated successfully"})
}

// Client Management Handlers

func (h *Handlers) HandleGetClients(c *gin.Context) {
	clients, err := h.repos.Clients.List()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get clients"})
		return
	}

	c.JSON(http.StatusOK, clients)
}

func (h *Handlers) HandleCreateClient(c *gin.Context) {
	var client models.VPNClient
	if err := c.ShouldBindJSON(&client); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate client data
	if err := h.validateClient(&client); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if common name already exists
	if exists, err := h.repos.Clients.IsCommonNameExists(client.CommonName); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check client existence"})
		return
	} else if exists {
		c.JSON(http.StatusConflict, gin.H{"error": "Client with this common name already exists"})
		return
	}

	// Create client in database
	if err := h.repos.Clients.Create(&client); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create client"})
		return
	}

	// Generate client certificate
	if err := h.clientManager.GenerateClientCertificate(&client); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate client certificate"})
		return
	}

	c.JSON(http.StatusCreated, client)
}

func (h *Handlers) HandleDeleteClient(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
		return
	}

	// Get client info before deletion
	client, err := h.repos.Clients.GetByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Client not found"})
		return
	}

	// Revoke client certificate
	if err := h.clientManager.RevokeClientCertificate(client); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to revoke client certificate"})
		return
	}

	// Delete client from database
	if err := h.repos.Clients.Delete(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete client"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Client deleted successfully"})
}

func (h *Handlers) HandleDownloadClientConfig(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
		return
	}

	// Get client info
	client, err := h.repos.Clients.GetByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Client not found"})
		return
	}

	// Get server configuration
	serverConfig, err := h.repos.Config.Get()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get server configuration"})
		return
	}

	// Generate client configuration
	configContent, err := h.clientManager.GenerateClientConfig(client, serverConfig)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate client configuration"})
		return
	}

	// Set headers for file download
	c.Header("Content-Disposition", "attachment; filename="+client.CommonName+".ovpn")
	c.Header("Content-Type", "application/octet-stream")
	c.String(http.StatusOK, configContent)
}

func (h *Handlers) HandleRevokeClient(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
		return
	}

	// Get client info
	client, err := h.repos.Clients.GetByID(id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Client not found"})
		return
	}

	// Revoke client certificate
	if err := h.clientManager.RevokeClientCertificate(client); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to revoke client certificate"})
		return
	}

	// Update client status in database
	if err := h.repos.Clients.Revoke(id); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update client status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Client certificate revoked successfully"})
}

// Monitoring Handlers

func (h *Handlers) HandleGetStatus(c *gin.Context) {
	stats, err := h.monitor.GetServerStatistics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get server statistics"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

func (h *Handlers) HandleGetLogs(c *gin.Context) {
	linesStr := c.DefaultQuery("lines", "100")
	lines, err := strconv.Atoi(linesStr)
	if err != nil {
		lines = 100
	}

	logs, err := h.monitor.GetServerLogs(lines)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get server logs"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"logs": logs})
}

func (h *Handlers) HandleGetConnections(c *gin.Context) {
	connections, err := h.monitor.GetConnectionStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get connection status"})
		return
	}

	c.JSON(http.StatusOK, connections)
}

// Validation functions

func (h *Handlers) validateServerConfig(config *models.ServerConfig) error {
	if config.Port < 1 || config.Port > 65535 {
		return fmt.Errorf("port must be between 1 and 65535")
	}

	if config.Protocol != "udp" && config.Protocol != "tcp" {
		return fmt.Errorf("protocol must be 'udp' or 'tcp'")
	}

	if config.MaxClients < 1 || config.MaxClients > 1000 {
		return fmt.Errorf("max clients must be between 1 and 1000")
	}

	// Validate network address
	if config.Network == "" {
		return fmt.Errorf("network address is required")
	}

	if config.Netmask == "" {
		return fmt.Errorf("netmask is required")
	}

	return nil
}

func (h *Handlers) validateClient(client *models.VPNClient) error {
	if client.Name == "" {
		return fmt.Errorf("client name is required")
	}

	if client.CommonName == "" {
		return fmt.Errorf("common name is required")
	}

	// Common name should not contain spaces or special characters
	for _, char := range client.CommonName {
		if !((char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') || char == '-' || char == '_') {
			return fmt.Errorf("common name can only contain letters, numbers, hyphens, and underscores")
		}
	}

	return nil
}
