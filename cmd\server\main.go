package main

import (
	"log"
	"vpn-manager/internal/api"
	"vpn-manager/internal/config"
	"vpn-manager/internal/database"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	log.Printf("Starting VPN Manager on port %s", cfg.ServerPort)

	// Initialize database
	db, err := database.Initialize(cfg.DatabasePath)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	log.Println("Database initialized successfully")

	// Start API server
	server := api.NewServer(cfg, db)

	log.Println("VPN Manager server started successfully")
	log.Printf("Web interface: http://localhost:%s", cfg.ServerPort)
	log.Printf("API endpoint: http://localhost:%s/api/v1", cfg.ServerPort)

	if err := server.Start(); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
