{{define "content"}}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-users"></i> Client Management</h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addClientModal">
                <i class="fas fa-plus"></i> Add Client
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list"></i> VPN Clients</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="clientsTable">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Common Name</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="text-center">Loading clients...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Client Modal -->
<div class="modal fade" id="addClientModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-user-plus"></i> Add New Client</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addClientForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="clientName" class="form-label">Client Name</label>
                        <input type="text" class="form-control" id="clientName" name="name" required>
                        <div class="form-text">Friendly name for the client</div>
                    </div>
                    <div class="mb-3">
                        <label for="commonName" class="form-label">Common Name</label>
                        <input type="text" class="form-control" id="commonName" name="common_name" required>
                        <div class="form-text">Unique identifier (letters, numbers, hyphens, underscores only)</div>
                    </div>
                    <div class="mb-3">
                        <label for="clientEmail" class="form-label">Email (Optional)</label>
                        <input type="email" class="form-control" id="clientEmail" name="email">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Client
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Client Details Modal -->
<div class="modal fade" id="clientDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-user"></i> Client Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="clientDetailsContent">
                    <!-- Content will be loaded dynamically -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" id="downloadConfigBtn">
                    <i class="fas fa-download"></i> Download Config
                </button>
                <button type="button" class="btn btn-warning" id="revokeClientBtn">
                    <i class="fas fa-ban"></i> Revoke
                </button>
                <button type="button" class="btn btn-danger" id="deleteClientBtn">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
    </div>
</div>

<script>
class ClientManager {
    constructor() {
        this.apiBase = '/api/v1';
        this.token = localStorage.getItem('vpn_token');
        this.currentClientId = null;
        this.init();
    }

    init() {
        this.loadClients();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Add client form
        document.getElementById('addClientForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.addClient();
        });

        // Client action buttons
        document.getElementById('downloadConfigBtn').addEventListener('click', () => {
            if (this.currentClientId) {
                this.downloadConfig(this.currentClientId);
            }
        });

        document.getElementById('revokeClientBtn').addEventListener('click', () => {
            if (this.currentClientId) {
                this.revokeClient(this.currentClientId);
            }
        });

        document.getElementById('deleteClientBtn').addEventListener('click', () => {
            if (this.currentClientId) {
                this.deleteClient(this.currentClientId);
            }
        });
    }

    async apiRequest(endpoint, options = {}) {
        const url = `${this.apiBase}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            ...options
        };

        const response = await fetch(url, config);
        
        if (response.status === 401) {
            window.location.href = '/login';
            return;
        }

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    async loadClients() {
        try {
            const clients = await this.apiRequest('/clients');
            this.renderClientsTable(clients);
        } catch (error) {
            console.error('Failed to load clients:', error);
            this.showAlert('Failed to load clients', 'danger');
        }
    }

    renderClientsTable(clients) {
        const tbody = document.querySelector('#clientsTable tbody');
        
        if (!clients || clients.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No clients found</td></tr>';
            return;
        }

        tbody.innerHTML = clients.map(client => `
            <tr>
                <td>${client.name}</td>
                <td><code>${client.common_name}</code></td>
                <td>${client.email || '-'}</td>
                <td>
                    <span class="badge ${client.is_active ? 'bg-success' : 'bg-danger'}">
                        ${client.is_active ? 'Active' : 'Revoked'}
                    </span>
                </td>
                <td>${new Date(client.created_at).toLocaleDateString()}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="clientManager.showClientDetails(${client.id})">
                        <i class="fas fa-eye"></i> Details
                    </button>
                </td>
            </tr>
        `).join('');
    }

    async addClient() {
        const form = document.getElementById('addClientForm');
        const formData = new FormData(form);
        
        const clientData = {
            name: formData.get('name'),
            common_name: formData.get('common_name'),
            email: formData.get('email'),
            is_active: true
        };

        try {
            await this.apiRequest('/clients', {
                method: 'POST',
                body: JSON.stringify(clientData)
            });

            this.showAlert('Client added successfully', 'success');
            form.reset();
            bootstrap.Modal.getInstance(document.getElementById('addClientModal')).hide();
            this.loadClients();
        } catch (error) {
            console.error('Failed to add client:', error);
            this.showAlert('Failed to add client', 'danger');
        }
    }

    async showClientDetails(clientId) {
        this.currentClientId = clientId;
        
        try {
            const clients = await this.apiRequest('/clients');
            const client = clients.find(c => c.id === clientId);
            
            if (!client) {
                this.showAlert('Client not found', 'danger');
                return;
            }

            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Basic Information</h6>
                        <p><strong>Name:</strong> ${client.name}</p>
                        <p><strong>Common Name:</strong> <code>${client.common_name}</code></p>
                        <p><strong>Email:</strong> ${client.email || 'Not provided'}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Status</h6>
                        <p><strong>Status:</strong> 
                            <span class="badge ${client.is_active ? 'bg-success' : 'bg-danger'}">
                                ${client.is_active ? 'Active' : 'Revoked'}
                            </span>
                        </p>
                        <p><strong>Created:</strong> ${new Date(client.created_at).toLocaleString()}</p>
                        ${client.revoked_at ? `<p><strong>Revoked:</strong> ${new Date(client.revoked_at).toLocaleString()}</p>` : ''}
                    </div>
                </div>
            `;

            document.getElementById('clientDetailsContent').innerHTML = content;
            new bootstrap.Modal(document.getElementById('clientDetailsModal')).show();
        } catch (error) {
            console.error('Failed to load client details:', error);
            this.showAlert('Failed to load client details', 'danger');
        }
    }

    async downloadConfig(clientId) {
        try {
            const response = await fetch(`${this.apiBase}/clients/${clientId}/config`, {
                headers: {
                    'Authorization': `Bearer ${this.token}`
                }
            });

            if (!response.ok) {
                throw new Error('Failed to download config');
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `client-${clientId}.ovpn`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.showAlert('Configuration downloaded successfully', 'success');
        } catch (error) {
            console.error('Failed to download config:', error);
            this.showAlert('Failed to download configuration', 'danger');
        }
    }

    async revokeClient(clientId) {
        if (!confirm('Are you sure you want to revoke this client certificate?')) {
            return;
        }

        try {
            await this.apiRequest(`/clients/${clientId}/revoke`, {
                method: 'POST'
            });

            this.showAlert('Client certificate revoked successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('clientDetailsModal')).hide();
            this.loadClients();
        } catch (error) {
            console.error('Failed to revoke client:', error);
            this.showAlert('Failed to revoke client certificate', 'danger');
        }
    }

    async deleteClient(clientId) {
        if (!confirm('Are you sure you want to delete this client? This action cannot be undone.')) {
            return;
        }

        try {
            await this.apiRequest(`/clients/${clientId}`, {
                method: 'DELETE'
            });

            this.showAlert('Client deleted successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('clientDetailsModal')).hide();
            this.loadClients();
        } catch (error) {
            console.error('Failed to delete client:', error);
            this.showAlert('Failed to delete client', 'danger');
        }
    }

    showAlert(message, type = 'info') {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const main = document.querySelector('main');
        if (main) {
            main.insertBefore(alert, main.firstChild);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.clientManager = new ClientManager();
});
</script>
{{end}}
