/* Custom styles for VPN Manager */

body {
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: bold;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-group .btn {
    margin-right: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.badge {
    font-size: 0.75em;
}

.text-muted {
    color: #6c757d !important;
}

/* Status indicators */
.status-online {
    color: #28a745;
}

.status-offline {
    color: #dc3545;
}

.status-warning {
    color: #ffc107;
}

/* Log container */
#log-container {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

#log-container .log-entry {
    margin-bottom: 2px;
    padding: 2px 0;
}

#log-container .log-entry.error {
    color: #dc3545;
}

#log-container .log-entry.warning {
    color: #ffc107;
}

#log-container .log-entry.info {
    color: #17a2b8;
}

/* Form styles */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group-text {
    background-color: #e9ecef;
    border-color: #ced4da;
}

/* Button styles */
.btn {
    border-radius: 0.375rem;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 0.25rem;
    }
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Alert styles */
.alert {
    border-radius: 0.375rem;
}

/* Progress bars */
.progress {
    height: 1rem;
}

/* Custom scrollbar for log container */
#log-container::-webkit-scrollbar {
    width: 8px;
}

#log-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#log-container::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

#log-container::-webkit-scrollbar-thumb:hover {
    background: #555;
}
