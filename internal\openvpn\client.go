package openvpn

import (
	"fmt"
	"os"
	"path/filepath"
	"vpn-manager/internal/models"
)

type ClientManager struct {
	service *Service
}

func NewClientManager(service *Service) *ClientManager {
	return &ClientManager{
		service: service,
	}
}

func (cm *ClientManager) GenerateClientCertificate(client *models.VPNClient) error {
	// In a production system, this would use easy-rsa or similar to generate real certificates
	// For now, we'll create placeholder certificates
	
	clientDir := filepath.Join(cm.service.certDir, "clients", client.CommonName)
	if err := os.MkdirAll(clientDir, 0755); err != nil {
		return fmt.Errorf("failed to create client directory: %v", err)
	}

	// Create placeholder client certificate files
	placeholderContent := fmt.Sprintf("# Placeholder client certificate for %s\n# Replace with actual certificates in production\n", client.CommonName)
	
	certFiles := []string{
		client.CommonName + ".crt",
		client.CommonName + ".key",
	}

	for _, file := range certFiles {
		path := filepath.Join(clientDir, file)
		if err := os.WriteFile(path, []byte(placeholderContent), 0644); err != nil {
			return fmt.Errorf("failed to create certificate file %s: %v", file, err)
		}
	}

	return nil
}

func (cm *ClientManager) GenerateClientConfig(client *models.VPNClient, serverConfig *models.ServerConfig) (string, error) {
	// Read certificate files
	clientDir := filepath.Join(cm.service.certDir, "clients", client.CommonName)
	
	caCert, err := os.ReadFile(filepath.Join(cm.service.certDir, "ca.crt"))
	if err != nil {
		return "", fmt.Errorf("failed to read CA certificate: %v", err)
	}

	clientCert, err := os.ReadFile(filepath.Join(clientDir, client.CommonName+".crt"))
	if err != nil {
		return "", fmt.Errorf("failed to read client certificate: %v", err)
	}

	clientKey, err := os.ReadFile(filepath.Join(clientDir, client.CommonName+".key"))
	if err != nil {
		return "", fmt.Errorf("failed to read client key: %v", err)
	}

	taKey, err := os.ReadFile(filepath.Join(cm.service.certDir, "ta.key"))
	if err != nil {
		return "", fmt.Errorf("failed to read TLS auth key: %v", err)
	}

	// Generate client configuration
	config := fmt.Sprintf(`# OpenVPN Client Configuration for %s
client
dev tun
proto %s
remote YOUR_SERVER_IP %d
resolv-retry infinite
nobind
persist-key
persist-tun

# Security
cipher %s
auth %s
key-direction 1
tls-auth [inline] 1

# Compression
%s

# Logging
verb 3
mute 20

<ca>
%s</ca>

<cert>
%s</cert>

<key>
%s</key>

<tls-auth>
%s</tls-auth>
`,
		client.Name,
		serverConfig.Protocol,
		serverConfig.Port,
		serverConfig.Cipher,
		serverConfig.Auth,
		cm.getCompressionConfig(serverConfig.Compression),
		string(caCert),
		string(clientCert),
		string(clientKey),
		string(taKey),
	)

	return config, nil
}

func (cm *ClientManager) RevokeClientCertificate(client *models.VPNClient) error {
	// In a production system, this would add the certificate to a CRL (Certificate Revocation List)
	// For now, we'll just remove the certificate files
	
	clientDir := filepath.Join(cm.service.certDir, "clients", client.CommonName)
	
	// Remove client certificate directory
	if err := os.RemoveAll(clientDir); err != nil {
		return fmt.Errorf("failed to remove client certificates: %v", err)
	}

	// Remove client-specific configuration if exists
	ccdFile := filepath.Join(cm.service.configDir, "ccd", client.CommonName)
	if _, err := os.Stat(ccdFile); err == nil {
		if err := os.Remove(ccdFile); err != nil {
			return fmt.Errorf("failed to remove client config: %v", err)
		}
	}

	return nil
}

func (cm *ClientManager) CreateClientSpecificConfig(client *models.VPNClient, staticIP string) error {
	// Create client-specific configuration for static IP assignment
	ccdDir := filepath.Join(cm.service.configDir, "ccd")
	if err := os.MkdirAll(ccdDir, 0755); err != nil {
		return fmt.Errorf("failed to create ccd directory: %v", err)
	}

	configPath := filepath.Join(ccdDir, client.CommonName)
	
	config := ""
	if staticIP != "" {
		// Assign static IP to client
		config = fmt.Sprintf("ifconfig-push %s *************\n", staticIP)
	}

	// Add any additional client-specific configurations here
	// For example, specific routes, bandwidth limits, etc.

	return os.WriteFile(configPath, []byte(config), 0644)
}

func (cm *ClientManager) GetActiveConnections() ([]*models.ActiveClient, error) {
	statusFile := filepath.Join(cm.service.configDir, "status.log")
	
	if _, err := os.Stat(statusFile); os.IsNotExist(err) {
		return []*models.ActiveClient{}, nil
	}

	file, err := os.Open(statusFile)
	if err != nil {
		return nil, fmt.Errorf("failed to open status file: %v", err)
	}
	defer file.Close()

	var clients []*models.ActiveClient
	
	// Parse OpenVPN status file
	// This is a simplified parser - in production you might want more robust parsing
	// Status file format varies by OpenVPN version
	
	// For now, return empty list as this requires actual OpenVPN status file format
	// In production, you would parse the actual status file format
	
	return clients, nil
}

func (cm *ClientManager) getCompressionConfig(compression string) string {
	switch compression {
	case "lz4-v2":
		return "compress lz4-v2"
	case "lz4":
		return "compress lz4"
	case "lzo":
		return "compress lzo"
	default:
		return "#compress"
	}
}

func (cm *ClientManager) ValidateClientConfig(config string) error {
	// Basic validation of client configuration
	// In production, you might want more comprehensive validation
	
	if config == "" {
		return fmt.Errorf("configuration cannot be empty")
	}

	// Check for required directives
	requiredDirectives := []string{"client", "dev", "proto", "remote"}
	
	for _, directive := range requiredDirectives {
		if !contains(config, directive) {
			return fmt.Errorf("missing required directive: %s", directive)
		}
	}

	return nil
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || 
		(len(s) > len(substr) && (s[:len(substr)] == substr || 
		s[len(s)-len(substr):] == substr || 
		containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
