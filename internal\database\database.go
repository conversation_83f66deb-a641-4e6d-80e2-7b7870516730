package database

import (
	"database/sql"
	"os"
	"path/filepath"

	_ "github.com/mattn/go-sqlite3"
)

type Repositories struct {
	Users   *UserRepository
	Clients *ClientRepository
	Config  *ConfigRepository
	Logs    *LogRepository
}

func Initialize(dbPath string) (*sql.DB, error) {
	// Create directory if it doesn't exist
	dir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, err
	}

	// Open database connection
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, err
	}

	// Test connection
	if err := db.Ping(); err != nil {
		return nil, err
	}

	// Create tables
	if err := createTables(db); err != nil {
		return nil, err
	}

	return db, nil
}

func NewRepositories(db *sql.DB) *Repositories {
	return &Repositories{
		Users:   NewUserRepository(db),
		Clients: NewClientRepository(db),
		Config:  NewConfigRepository(db),
		Logs:    NewLogRepository(db),
	}
}

func createTables(db *sql.DB) error {
	queries := []string{
		`CREATE TABLE IF NOT EXISTS users (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			username TEXT UNIQUE NOT NULL,
			password TEXT NOT NULL,
			role TEXT NOT NULL DEFAULT 'user',
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)`,

		`CREATE TABLE IF NOT EXISTS vpn_clients (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name TEXT NOT NULL,
			common_name TEXT UNIQUE NOT NULL,
			email TEXT,
			is_active BOOLEAN DEFAULT 1,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			revoked_at DATETIME
		)`,

		`CREATE TABLE IF NOT EXISTS server_config (
			id INTEGER PRIMARY KEY,
			port INTEGER DEFAULT 1194,
			protocol TEXT DEFAULT 'udp',
			cipher TEXT DEFAULT 'AES-256-GCM',
			auth TEXT DEFAULT 'SHA256',
			compression TEXT DEFAULT 'lz4-v2',
			network TEXT DEFAULT '********',
			netmask TEXT DEFAULT '*************',
			dns1 TEXT DEFAULT '*******',
			dns2 TEXT DEFAULT '*******',
			redirect_gateway BOOLEAN DEFAULT 1,
			duplicate_cn BOOLEAN DEFAULT 0,
			client_to_client BOOLEAN DEFAULT 0,
			max_clients INTEGER DEFAULT 100,
			keep_alive TEXT DEFAULT '10 120',
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)`,

		`CREATE TABLE IF NOT EXISTS connection_logs (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			client_name TEXT NOT NULL,
			client_ip TEXT NOT NULL,
			connected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			disconnected_at DATETIME,
			bytes_received INTEGER DEFAULT 0,
			bytes_sent INTEGER DEFAULT 0
		)`,

		`CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)`,
		`CREATE INDEX IF NOT EXISTS idx_vpn_clients_common_name ON vpn_clients(common_name)`,
		`CREATE INDEX IF NOT EXISTS idx_connection_logs_client_name ON connection_logs(client_name)`,
		`CREATE INDEX IF NOT EXISTS idx_connection_logs_connected_at ON connection_logs(connected_at)`,
	}

	for _, query := range queries {
		if _, err := db.Exec(query); err != nil {
			return err
		}
	}

	// Insert default server configuration if not exists
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM server_config").Scan(&count)
	if err != nil {
		return err
	}

	if count == 0 {
		_, err = db.Exec(`INSERT INTO server_config (id) VALUES (1)`)
		if err != nil {
			return err
		}
	}

	return nil
}
