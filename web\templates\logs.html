{{define "content"}}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-file-alt"></i> Server Logs</h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="refreshLogsBtn">
                    <i class="fas fa-sync"></i> Refresh
                </button>
                <button type="button" class="btn btn-outline-secondary" id="clearLogsBtn">
                    <i class="fas fa-trash"></i> Clear Display
                </button>
                <button type="button" class="btn btn-outline-info" id="autoRefreshBtn">
                    <i class="fas fa-play"></i> Auto Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title"><i class="fas fa-filter"></i> Log Filters</h6>
                <div class="row">
                    <div class="col-md-6">
                        <label for="logLevel" class="form-label">Level</label>
                        <select class="form-select form-select-sm" id="logLevel">
                            <option value="">All Levels</option>
                            <option value="error">Error</option>
                            <option value="warning">Warning</option>
                            <option value="info">Info</option>
                            <option value="debug">Debug</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="logLines" class="form-label">Lines</label>
                        <select class="form-select form-select-sm" id="logLines">
                            <option value="50">50 lines</option>
                            <option value="100" selected>100 lines</option>
                            <option value="200">200 lines</option>
                            <option value="500">500 lines</option>
                            <option value="1000">1000 lines</option>
                        </select>
                    </div>
                </div>
                <div class="mt-2">
                    <input type="text" class="form-control form-control-sm" id="searchFilter" placeholder="Search logs...">
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title"><i class="fas fa-chart-bar"></i> Log Statistics</h6>
                <div class="row text-center">
                    <div class="col-3">
                        <div class="text-danger">
                            <strong id="errorCount">0</strong>
                            <br><small>Errors</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-warning">
                            <strong id="warningCount">0</strong>
                            <br><small>Warnings</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-info">
                            <strong id="infoCount">0</strong>
                            <br><small>Info</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="text-muted">
                            <strong id="totalCount">0</strong>
                            <br><small>Total</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-terminal"></i> Live Logs</h5>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="followLogs" checked>
                    <label class="form-check-label" for="followLogs">
                        Auto-scroll to bottom
                    </label>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="logContainer" class="log-container">
                    <div class="text-center text-muted p-4">
                        <i class="fas fa-spinner fa-spin"></i> Loading logs...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.log-container {
    height: 600px;
    overflow-y: auto;
    background-color: #1e1e1e;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    padding: 15px;
    border-radius: 0 0 0.375rem 0.375rem;
}

.log-entry {
    margin-bottom: 2px;
    padding: 2px 0;
    word-wrap: break-word;
    white-space: pre-wrap;
}

.log-entry.error {
    color: #ff6b6b;
    background-color: rgba(255, 107, 107, 0.1);
}

.log-entry.warning {
    color: #ffd93d;
    background-color: rgba(255, 217, 61, 0.1);
}

.log-entry.info {
    color: #74c0fc;
    background-color: rgba(116, 192, 252, 0.1);
}

.log-entry.debug {
    color: #51cf66;
}

.log-entry.highlight {
    background-color: rgba(255, 255, 0, 0.2);
}

.log-timestamp {
    color: #868e96;
    margin-right: 8px;
}

.log-level {
    font-weight: bold;
    margin-right: 8px;
    min-width: 60px;
    display: inline-block;
}

.log-container::-webkit-scrollbar {
    width: 8px;
}

.log-container::-webkit-scrollbar-track {
    background: #2c2c2c;
}

.log-container::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
    background: #777;
}
</style>

<script>
class LogManager {
    constructor() {
        this.apiBase = '/api/v1';
        this.token = localStorage.getItem('vpn_token');
        this.autoRefreshInterval = null;
        this.isAutoRefreshEnabled = false;
        this.logs = [];
        this.filteredLogs = [];
        this.init();
    }

    init() {
        this.loadLogs();
        this.setupEventListeners();
    }

    setupEventListeners() {
        document.getElementById('refreshLogsBtn').addEventListener('click', () => {
            this.loadLogs();
        });

        document.getElementById('clearLogsBtn').addEventListener('click', () => {
            this.clearDisplay();
        });

        document.getElementById('autoRefreshBtn').addEventListener('click', () => {
            this.toggleAutoRefresh();
        });

        document.getElementById('logLevel').addEventListener('change', () => {
            this.filterLogs();
        });

        document.getElementById('logLines').addEventListener('change', () => {
            this.loadLogs();
        });

        document.getElementById('searchFilter').addEventListener('input', () => {
            this.filterLogs();
        });
    }

    async apiRequest(endpoint, options = {}) {
        const url = `${this.apiBase}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            ...options
        };

        const response = await fetch(url, config);
        
        if (response.status === 401) {
            window.location.href = '/login';
            return;
        }

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    async loadLogs() {
        try {
            const lines = document.getElementById('logLines').value;
            const response = await this.apiRequest(`/logs?lines=${lines}`);
            
            if (response.logs) {
                this.logs = response.logs.map(log => this.parseLogEntry(log));
                this.filterLogs();
                this.updateStatistics();
            }
        } catch (error) {
            console.error('Failed to load logs:', error);
            this.showError('Failed to load logs');
        }
    }

    parseLogEntry(logLine) {
        // Simple log parsing - in production you might want more sophisticated parsing
        const timestamp = new Date().toISOString();
        let level = 'info';
        
        if (logLine.includes('ERROR') || logLine.includes('error')) {
            level = 'error';
        } else if (logLine.includes('WARNING') || logLine.includes('warning')) {
            level = 'warning';
        } else if (logLine.includes('INFO') || logLine.includes('info')) {
            level = 'info';
        } else if (logLine.includes('DEBUG') || logLine.includes('debug')) {
            level = 'debug';
        }

        return {
            timestamp,
            level,
            message: logLine,
            raw: logLine
        };
    }

    filterLogs() {
        const levelFilter = document.getElementById('logLevel').value;
        const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

        this.filteredLogs = this.logs.filter(log => {
            const levelMatch = !levelFilter || log.level === levelFilter;
            const searchMatch = !searchFilter || log.message.toLowerCase().includes(searchFilter);
            return levelMatch && searchMatch;
        });

        this.renderLogs();
    }

    renderLogs() {
        const container = document.getElementById('logContainer');
        
        if (this.filteredLogs.length === 0) {
            container.innerHTML = '<div class="text-center text-muted p-4">No logs found</div>';
            return;
        }

        const searchTerm = document.getElementById('searchFilter').value.toLowerCase();
        
        const logsHtml = this.filteredLogs.map(log => {
            let message = log.message;
            
            // Highlight search terms
            if (searchTerm) {
                const regex = new RegExp(`(${searchTerm})`, 'gi');
                message = message.replace(regex, '<mark>$1</mark>');
            }

            return `
                <div class="log-entry ${log.level}">
                    <span class="log-timestamp">${new Date(log.timestamp).toLocaleTimeString()}</span>
                    <span class="log-level">[${log.level.toUpperCase()}]</span>
                    <span class="log-message">${message}</span>
                </div>
            `;
        }).join('');

        container.innerHTML = logsHtml;

        // Auto-scroll to bottom if enabled
        if (document.getElementById('followLogs').checked) {
            container.scrollTop = container.scrollHeight;
        }
    }

    updateStatistics() {
        const stats = {
            error: 0,
            warning: 0,
            info: 0,
            debug: 0,
            total: this.logs.length
        };

        this.logs.forEach(log => {
            if (stats.hasOwnProperty(log.level)) {
                stats[log.level]++;
            }
        });

        document.getElementById('errorCount').textContent = stats.error;
        document.getElementById('warningCount').textContent = stats.warning;
        document.getElementById('infoCount').textContent = stats.info;
        document.getElementById('totalCount').textContent = stats.total;
    }

    clearDisplay() {
        document.getElementById('logContainer').innerHTML = '<div class="text-center text-muted p-4">Logs cleared</div>';
        this.logs = [];
        this.filteredLogs = [];
        this.updateStatistics();
    }

    toggleAutoRefresh() {
        const btn = document.getElementById('autoRefreshBtn');
        
        if (this.isAutoRefreshEnabled) {
            // Stop auto refresh
            clearInterval(this.autoRefreshInterval);
            this.isAutoRefreshEnabled = false;
            btn.innerHTML = '<i class="fas fa-play"></i> Auto Refresh';
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-info');
        } else {
            // Start auto refresh
            this.autoRefreshInterval = setInterval(() => {
                this.loadLogs();
            }, 5000); // Refresh every 5 seconds
            
            this.isAutoRefreshEnabled = true;
            btn.innerHTML = '<i class="fas fa-pause"></i> Stop Auto Refresh';
            btn.classList.remove('btn-outline-info');
            btn.classList.add('btn-success');
        }
    }

    showError(message) {
        const container = document.getElementById('logContainer');
        container.innerHTML = `
            <div class="text-center text-danger p-4">
                <i class="fas fa-exclamation-triangle"></i> ${message}
            </div>
        `;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new LogManager();
});
</script>
{{end}}
