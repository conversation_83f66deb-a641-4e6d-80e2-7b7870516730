package database

import (
	"database/sql"
	"time"
	"vpn-manager/internal/models"
)

type ClientRepository struct {
	db *sql.DB
}

func NewClientRepository(db *sql.DB) *ClientRepository {
	return &ClientRepository{db: db}
}

func (r *ClientRepository) Create(client *models.VPNClient) error {
	query := `
		INSERT INTO vpn_clients (name, common_name, email, is_active, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?)
	`
	
	now := time.Now()
	result, err := r.db.Exec(query, client.Name, client.CommonName, client.Email, client.IsActive, now, now)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	client.ID = int(id)
	client.CreatedAt = now
	client.UpdatedAt = now
	return nil
}

func (r *ClientRepository) GetByID(id int) (*models.VPNClient, error) {
	query := `
		SELECT id, name, common_name, email, is_active, created_at, updated_at, revoked_at
		FROM vpn_clients
		WHERE id = ?
	`
	
	client := &models.VPNClient{}
	err := r.db.QueryRow(query, id).Scan(
		&client.ID,
		&client.Name,
		&client.CommonName,
		&client.Email,
		&client.IsActive,
		&client.CreatedAt,
		&client.UpdatedAt,
		&client.RevokedAt,
	)
	
	if err != nil {
		return nil, err
	}
	
	return client, nil
}

func (r *ClientRepository) GetByCommonName(commonName string) (*models.VPNClient, error) {
	query := `
		SELECT id, name, common_name, email, is_active, created_at, updated_at, revoked_at
		FROM vpn_clients
		WHERE common_name = ?
	`
	
	client := &models.VPNClient{}
	err := r.db.QueryRow(query, commonName).Scan(
		&client.ID,
		&client.Name,
		&client.CommonName,
		&client.Email,
		&client.IsActive,
		&client.CreatedAt,
		&client.UpdatedAt,
		&client.RevokedAt,
	)
	
	if err != nil {
		return nil, err
	}
	
	return client, nil
}

func (r *ClientRepository) List() ([]*models.VPNClient, error) {
	query := `
		SELECT id, name, common_name, email, is_active, created_at, updated_at, revoked_at
		FROM vpn_clients
		ORDER BY created_at DESC
	`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var clients []*models.VPNClient
	for rows.Next() {
		client := &models.VPNClient{}
		err := rows.Scan(
			&client.ID,
			&client.Name,
			&client.CommonName,
			&client.Email,
			&client.IsActive,
			&client.CreatedAt,
			&client.UpdatedAt,
			&client.RevokedAt,
		)
		if err != nil {
			return nil, err
		}
		clients = append(clients, client)
	}

	return clients, nil
}

func (r *ClientRepository) ListActive() ([]*models.VPNClient, error) {
	query := `
		SELECT id, name, common_name, email, is_active, created_at, updated_at, revoked_at
		FROM vpn_clients
		WHERE is_active = 1 AND revoked_at IS NULL
		ORDER BY created_at DESC
	`
	
	rows, err := r.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var clients []*models.VPNClient
	for rows.Next() {
		client := &models.VPNClient{}
		err := rows.Scan(
			&client.ID,
			&client.Name,
			&client.CommonName,
			&client.Email,
			&client.IsActive,
			&client.CreatedAt,
			&client.UpdatedAt,
			&client.RevokedAt,
		)
		if err != nil {
			return nil, err
		}
		clients = append(clients, client)
	}

	return clients, nil
}

func (r *ClientRepository) Update(client *models.VPNClient) error {
	query := `
		UPDATE vpn_clients
		SET name = ?, email = ?, is_active = ?, updated_at = ?
		WHERE id = ?
	`
	
	now := time.Now()
	_, err := r.db.Exec(query, client.Name, client.Email, client.IsActive, now, client.ID)
	if err != nil {
		return err
	}

	client.UpdatedAt = now
	return nil
}

func (r *ClientRepository) Revoke(id int) error {
	query := `
		UPDATE vpn_clients
		SET is_active = 0, revoked_at = ?, updated_at = ?
		WHERE id = ?
	`
	
	now := time.Now()
	_, err := r.db.Exec(query, now, now, id)
	return err
}

func (r *ClientRepository) Delete(id int) error {
	query := `DELETE FROM vpn_clients WHERE id = ?`
	_, err := r.db.Exec(query, id)
	return err
}

func (r *ClientRepository) IsCommonNameExists(commonName string) (bool, error) {
	query := `SELECT COUNT(*) FROM vpn_clients WHERE common_name = ?`
	
	var count int
	err := r.db.QueryRow(query, commonName).Scan(&count)
	if err != nil {
		return false, err
	}
	
	return count > 0, nil
}
