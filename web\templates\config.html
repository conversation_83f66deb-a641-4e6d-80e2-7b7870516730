{{define "content"}}
<div class="row">
    <div class="col-12">
        <h1><i class="fas fa-cog"></i> Server Configuration</h1>
        <p class="text-muted">Configure OpenVPN server settings. Changes require server restart to take effect.</p>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-server"></i> Server Settings</h5>
            </div>
            <div class="card-body">
                <form id="configForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="port" class="form-label">Port</label>
                                <input type="number" class="form-control" id="port" name="port" min="1" max="65535" required>
                                <div class="form-text">Port number for OpenVPN server (default: 1194)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="protocol" class="form-label">Protocol</label>
                                <select class="form-select" id="protocol" name="protocol" required>
                                    <option value="udp">UDP (Recommended)</option>
                                    <option value="tcp">TCP</option>
                                </select>
                                <div class="form-text">Network protocol to use</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cipher" class="form-label">Cipher</label>
                                <select class="form-select" id="cipher" name="cipher" required>
                                    <option value="AES-256-GCM">AES-256-GCM (Recommended)</option>
                                    <option value="AES-128-GCM">AES-128-GCM</option>
                                    <option value="AES-256-CBC">AES-256-CBC</option>
                                    <option value="AES-128-CBC">AES-128-CBC</option>
                                </select>
                                <div class="form-text">Encryption cipher</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="auth" class="form-label">Authentication</label>
                                <select class="form-select" id="auth" name="auth" required>
                                    <option value="SHA256">SHA256 (Recommended)</option>
                                    <option value="SHA1">SHA1</option>
                                    <option value="SHA512">SHA512</option>
                                </select>
                                <div class="form-text">HMAC authentication algorithm</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="compression" class="form-label">Compression</label>
                                <select class="form-select" id="compression" name="compression">
                                    <option value="lz4-v2">LZ4-v2 (Recommended)</option>
                                    <option value="lz4">LZ4</option>
                                    <option value="lzo">LZO</option>
                                    <option value="">None</option>
                                </select>
                                <div class="form-text">Data compression algorithm</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_clients" class="form-label">Max Clients</label>
                                <input type="number" class="form-control" id="max_clients" name="max_clients" min="1" max="1000" required>
                                <div class="form-text">Maximum number of concurrent clients</div>
                            </div>
                        </div>
                    </div>

                    <h6 class="mt-4 mb-3"><i class="fas fa-network-wired"></i> Network Settings</h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="network" class="form-label">VPN Network</label>
                                <input type="text" class="form-control" id="network" name="network" required>
                                <div class="form-text">VPN subnet (e.g., ********)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="netmask" class="form-label">Netmask</label>
                                <input type="text" class="form-control" id="netmask" name="netmask" required>
                                <div class="form-text">Subnet mask (e.g., *************)</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dns1" class="form-label">Primary DNS</label>
                                <input type="text" class="form-control" id="dns1" name="dns1" required>
                                <div class="form-text">Primary DNS server</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dns2" class="form-label">Secondary DNS</label>
                                <input type="text" class="form-control" id="dns2" name="dns2">
                                <div class="form-text">Secondary DNS server (optional)</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="keep_alive" class="form-label">Keep Alive</label>
                        <input type="text" class="form-control" id="keep_alive" name="keep_alive" required>
                        <div class="form-text">Keep alive settings (e.g., "10 120")</div>
                    </div>

                    <h6 class="mt-4 mb-3"><i class="fas fa-shield-alt"></i> Security Options</h6>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="redirect_gateway" name="redirect_gateway">
                                <label class="form-check-label" for="redirect_gateway">
                                    Redirect Gateway
                                </label>
                                <div class="form-text">Route all client traffic through VPN</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="client_to_client" name="client_to_client">
                                <label class="form-check-label" for="client_to_client">
                                    Client-to-Client
                                </label>
                                <div class="form-text">Allow clients to communicate with each other</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="duplicate_cn" name="duplicate_cn">
                                <label class="form-check-label" for="duplicate_cn">
                                    Duplicate CN
                                </label>
                                <div class="form-text">Allow multiple connections with same certificate</div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" id="resetConfigBtn">
                            <i class="fas fa-undo"></i> Reset to Defaults
                        </button>
                        <div>
                            <button type="button" class="btn btn-outline-primary me-2" id="loadConfigBtn">
                                <i class="fas fa-sync"></i> Reload
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Configuration
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Configuration Help</h5>
            </div>
            <div class="card-body">
                <h6>Port & Protocol</h6>
                <p class="small">UDP is recommended for better performance. Use TCP if UDP is blocked by firewalls.</p>
                
                <h6>Encryption</h6>
                <p class="small">AES-256-GCM provides the best security and performance for modern systems.</p>
                
                <h6>Network Settings</h6>
                <p class="small">Choose a private network range that doesn't conflict with your local networks.</p>
                
                <h6>DNS Servers</h6>
                <p class="small">Common options: Google (*******), Cloudflare (*******), or your local DNS server.</p>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Important:</strong> Server must be restarted for changes to take effect.
                </div>
            </div>
        </div>
    </div>
</div>

<script>
class ConfigManager {
    constructor() {
        this.apiBase = '/api/v1';
        this.token = localStorage.getItem('vpn_token');
        this.init();
    }

    init() {
        this.loadConfiguration();
        this.setupEventListeners();
    }

    setupEventListeners() {
        document.getElementById('configForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveConfiguration();
        });

        document.getElementById('loadConfigBtn').addEventListener('click', () => {
            this.loadConfiguration();
        });

        document.getElementById('resetConfigBtn').addEventListener('click', () => {
            this.resetToDefaults();
        });
    }

    async apiRequest(endpoint, options = {}) {
        const url = `${this.apiBase}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            ...options
        };

        const response = await fetch(url, config);
        
        if (response.status === 401) {
            window.location.href = '/login';
            return;
        }

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    async loadConfiguration() {
        try {
            const config = await this.apiRequest('/config');
            this.populateForm(config);
            this.showAlert('Configuration loaded successfully', 'success');
        } catch (error) {
            console.error('Failed to load configuration:', error);
            this.showAlert('Failed to load configuration', 'danger');
        }
    }

    populateForm(config) {
        document.getElementById('port').value = config.port || 1194;
        document.getElementById('protocol').value = config.protocol || 'udp';
        document.getElementById('cipher').value = config.cipher || 'AES-256-GCM';
        document.getElementById('auth').value = config.auth || 'SHA256';
        document.getElementById('compression').value = config.compression || 'lz4-v2';
        document.getElementById('max_clients').value = config.max_clients || 100;
        document.getElementById('network').value = config.network || '********';
        document.getElementById('netmask').value = config.netmask || '*************';
        document.getElementById('dns1').value = config.dns1 || '*******';
        document.getElementById('dns2').value = config.dns2 || '*******';
        document.getElementById('keep_alive').value = config.keep_alive || '10 120';
        document.getElementById('redirect_gateway').checked = config.redirect_gateway || false;
        document.getElementById('client_to_client').checked = config.client_to_client || false;
        document.getElementById('duplicate_cn').checked = config.duplicate_cn || false;
    }

    async saveConfiguration() {
        const form = document.getElementById('configForm');
        const formData = new FormData(form);
        
        const config = {
            port: parseInt(formData.get('port')),
            protocol: formData.get('protocol'),
            cipher: formData.get('cipher'),
            auth: formData.get('auth'),
            compression: formData.get('compression'),
            max_clients: parseInt(formData.get('max_clients')),
            network: formData.get('network'),
            netmask: formData.get('netmask'),
            dns1: formData.get('dns1'),
            dns2: formData.get('dns2'),
            keep_alive: formData.get('keep_alive'),
            redirect_gateway: formData.has('redirect_gateway'),
            client_to_client: formData.has('client_to_client'),
            duplicate_cn: formData.has('duplicate_cn')
        };

        try {
            await this.apiRequest('/config', {
                method: 'PUT',
                body: JSON.stringify(config)
            });

            this.showAlert('Configuration saved successfully. Restart server for changes to take effect.', 'success');
        } catch (error) {
            console.error('Failed to save configuration:', error);
            this.showAlert('Failed to save configuration', 'danger');
        }
    }

    resetToDefaults() {
        if (!confirm('Are you sure you want to reset all settings to defaults?')) {
            return;
        }

        const defaults = {
            port: 1194,
            protocol: 'udp',
            cipher: 'AES-256-GCM',
            auth: 'SHA256',
            compression: 'lz4-v2',
            max_clients: 100,
            network: '********',
            netmask: '*************',
            dns1: '*******',
            dns2: '*******',
            keep_alive: '10 120',
            redirect_gateway: true,
            client_to_client: false,
            duplicate_cn: false
        };

        this.populateForm(defaults);
        this.showAlert('Configuration reset to defaults', 'info');
    }

    showAlert(message, type = 'info') {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const main = document.querySelector('main');
        if (main) {
            main.insertBefore(alert, main.firstChild);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ConfigManager();
});
</script>
{{end}}
