package auth

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"vpn-manager/internal/database"
)

type AuthHandlers struct {
	userRepo   *database.UserRepository
	jwtManager *JWTManager
}

func NewAuthHandlers(userRepo *database.UserRepository, jwtManager *JWTManager) *AuthHandlers {
	return &AuthHandlers{
		userRepo:   userRepo,
		jwtManager: jwtManager,
	}
}

type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type LoginResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	User      UserInfo  `json:"user"`
}

func (h *AuthHandlers) HandleLogin(c *gin.Context) {
	var req LoginRequest
	if err := c.<PERSON>ind<PERSON>(&req); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user from database
	user, err := h.userRepo.GetByUsername(req.Username)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Validate password
	if !h.userRepo.ValidatePassword(user, req.Password) {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Generate JWT token
	token, err := h.jwtManager.GenerateToken(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	// Calculate expiry time
	expiresAt := time.Now().Add(24 * time.Hour) // Assuming 24 hour token TTL

	response := LoginResponse{
		Token:     token,
		ExpiresAt: expiresAt,
		User: UserInfo{
			ID:       user.ID,
			Username: user.Username,
			Role:     user.Role,
		},
	}

	c.JSON(http.StatusOK, response)
}

func (h *AuthHandlers) HandleRefreshToken(c *gin.Context) {
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
		return
	}

	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	if tokenString == authHeader {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Bearer token required"})
		return
	}

	// Refresh token
	newToken, err := h.jwtManager.RefreshToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Failed to refresh token"})
		return
	}

	// Get user info from token
	userInfo, err := h.jwtManager.ExtractUserInfo(newToken)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to extract user info"})
		return
	}

	response := LoginResponse{
		Token:     newToken,
		ExpiresAt: time.Now().Add(24 * time.Hour),
		User:      *userInfo,
	}

	c.JSON(http.StatusOK, response)
}

func (h *AuthHandlers) HandleLogout(c *gin.Context) {
	// In a more sophisticated system, you might maintain a blacklist of tokens
	// For now, we'll just return success as the client should discard the token
	c.JSON(http.StatusOK, gin.H{"message": "Logged out successfully"})
}

func (h *AuthHandlers) HandleGetProfile(c *gin.Context) {
	userInfo, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	user := userInfo.(*UserInfo)
	c.JSON(http.StatusOK, user)
}

type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=6"`
}

func (h *AuthHandlers) HandleChangePassword(c *gin.Context) {
	userInfo, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	user := userInfo.(*UserInfo)

	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get full user record from database
	dbUser, err := h.userRepo.GetByID(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user"})
		return
	}

	// Validate current password
	if !h.userRepo.ValidatePassword(dbUser, req.CurrentPassword) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Current password is incorrect"})
		return
	}

	// Update password
	if err := h.userRepo.UpdatePassword(user.ID, req.NewPassword); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update password"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Password updated successfully"})
}

// Middleware functions

func (h *AuthHandlers) AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Bearer token required"})
			c.Abort()
			return
		}

		// Validate token
		userInfo, err := h.jwtManager.ExtractUserInfo(tokenString)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		// Set user info in context
		c.Set("user", userInfo)
		c.Next()
	}
}

func (h *AuthHandlers) RequireRole(role string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userInfo, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			c.Abort()
			return
		}

		user := userInfo.(*UserInfo)
		if user.Role != role && user.Role != "admin" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
			c.Abort()
			return
		}

		c.Next()
	}
}

func (h *AuthHandlers) RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userInfo, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			c.Abort()
			return
		}

		user := userInfo.(*UserInfo)
		hasPermission := false

		switch permission {
		case "manage_clients":
			hasPermission = user.CanManageClients()
		case "view_logs":
			hasPermission = user.CanViewLogs()
		case "control_server":
			hasPermission = user.CanControlServer()
		case "admin":
			hasPermission = user.IsAdmin()
		default:
			hasPermission = false
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
			c.Abort()
			return
		}

		c.Next()
	}
}
